const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动刻晴桌面宠物开发环境...\n');

// 启动渲染进程开发服务器
console.log('📦 启动渲染进程开发服务器...');
const rendererProcess = spawn('npm', ['run', 'dev:renderer'], {
  stdio: 'inherit',
  shell: true,
  cwd: path.resolve(__dirname, '..')
});

// 等待一段时间让渲染进程服务器启动
setTimeout(() => {
  console.log('⚡ 启动主进程...');
  const mainProcess = spawn('npm', ['run', 'dev:main'], {
    stdio: 'inherit',
    shell: true,
    cwd: path.resolve(__dirname, '..')
  });

  // 处理进程退出
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭开发服务器...');
    rendererProcess.kill();
    mainProcess.kill();
    process.exit();
  });
}, 3000);

console.log('\n✨ 开发环境启动完成！');
console.log('📝 修改代码将自动重新加载');
console.log('🔧 按 Ctrl+C 停止开发服务器\n');