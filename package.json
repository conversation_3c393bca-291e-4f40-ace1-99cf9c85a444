{"name": "keqing-desktop-pet", "version": "1.0.0", "description": "刻晴3D桌面宠物 - 一个可交互的3D桌面伴侣应用", "main": "dist/main/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:main": "webpack --config webpack.main.config.js --mode development --watch", "dev:renderer": "webpack serve --config webpack.renderer.config.js --mode development", "build": "npm run build:main && npm run build:renderer", "build:main": "npx webpack --config webpack.main.config.js --mode production", "build:renderer": "npx webpack --config webpack.renderer.config.js --mode production", "start": "electron .", "pack": "electron-builder", "dist": "npm run build && electron-builder", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["electron", "desktop-pet", "3d", "keqing", "genshin"], "author": "Keqing Desktop Pet Team", "license": "MIT", "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^13.4.0", "@types/jest": "^29.5.14", "@types/node": "^24.3.0", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@types/three": "^0.179.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "concurrently": "^8.2.1", "copy-webpack-plugin": "^13.0.1", "css-loader": "^6.11.0", "electron": "^37.4.0", "electron-builder": "^24.6.4", "eslint": "^8.49.0", "eslint-plugin-react": "^7.33.2", "html-webpack-plugin": "^5.6.4", "jest": "^29.7.0", "jest-environment-jsdom": "^30.1.1", "style-loader": "^3.3.4", "ts-jest": "^29.4.1", "ts-loader": "^9.5.4", "typescript": "^5.9.2", "webpack": "^5.101.3", "webpack-cli": "^6.0.1", "webpack-dev-server": "^4.15.1"}, "dependencies": {"@pixiv/three-vrm": "^3.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "three": "^0.164.1", "zustand": "^5.0.8"}, "build": {"appId": "com.keqing.desktop-pet", "productName": "刻晴桌面宠物", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "mac": {"category": "public.app-category.entertainment"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}