{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "outDir": "./dist", "baseUrl": "./src", "paths": {"@/*": ["*"], "@main/*": ["main/*"], "@renderer/*": ["renderer/*"], "@shared/*": ["shared/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "release"]}