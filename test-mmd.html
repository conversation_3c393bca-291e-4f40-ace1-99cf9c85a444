<!DOCTYPE html>
<html>
<head>
    <title>MMD加载测试</title>
    <style>
        body { margin: 0; padding: 0; background: #000; }
        canvas { display: block; }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-family: Arial;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="info">MMD模型加载测试</div>
    <script type="module">
        import * as THREE from 'https://unpkg.com/three@0.164.1/build/three.module.js';
        import { MMDLoader } from 'https://unpkg.com/three@0.164.1/examples/jsm/loaders/MMDLoader.js';

        // 创建场景
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer();
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x222222);
        document.body.appendChild(renderer.domElement);

        // 添加光源
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);

        // 设置相机位置
        camera.position.z = 5;

        // 创建MMD加载器
        const loader = new MMDLoader();

        // 尝试加载PMX模型
        console.log('开始加载PMX模型...');
        
        loader.load(
            './assets/models/刻晴.pmx',
            (mmd) => {
                console.log('PMX模型加载成功!', mmd);
                scene.add(mmd);
                
                // 调整模型位置和大小
                mmd.position.y = -1;
                mmd.scale.setScalar(0.1);
                
                document.getElementById('info').textContent = 'PMX模型加载成功！';
            },
            (progress) => {
                console.log('加载进度:', progress);
            },
            (error) => {
                console.error('PMX模型加载失败:', error);
                document.getElementById('info').textContent = 'PMX模型加载失败，使用刻晴风格占位符';
                
                // 创建刻晴风格的占位符模型
                createKeqingPlaceholder();
            }
        );

        function createKeqingPlaceholder() {
            const group = new THREE.Group();
            
            // 刻晴的紫色主题色
            const keqingPurple = 0x6B46C1;
            const keqingLightPurple = 0x8B5CF6;
            const skinColor = 0xFFDBB5;
            const hairColor = 0x4C1D95;
            
            // 身体
            const bodyGeometry = new THREE.CapsuleGeometry(0.4, 1.8, 8, 16);
            const bodyMaterial = new THREE.MeshLambertMaterial({ color: keqingPurple });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.y = 0.8;
            group.add(body);

            // 头部
            const headGeometry = new THREE.SphereGeometry(0.35, 16, 16);
            const headMaterial = new THREE.MeshLambertMaterial({ color: skinColor });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.y = 2.2;
            group.add(head);

            // 头发
            const hairGeometry = new THREE.SphereGeometry(0.38, 16, 16);
            const hairMaterial = new THREE.MeshLambertMaterial({ 
                color: hairColor, 
                transparent: true, 
                opacity: 0.8 
            });
            const hair = new THREE.Mesh(hairGeometry, hairMaterial);
            hair.position.y = 2.3;
            hair.scale.set(1, 0.8, 1);
            group.add(hair);

            // 双马尾
            const ponytailGeometry = new THREE.SphereGeometry(0.15, 8, 8);
            const ponytailMaterial = new THREE.MeshLambertMaterial({ 
                color: hairColor, 
                transparent: true, 
                opacity: 0.9 
            });
            
            const leftPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
            leftPonytail.position.set(-0.4, 2.1, -0.2);
            leftPonytail.scale.set(1, 2, 1);
            group.add(leftPonytail);
            
            const rightPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
            rightPonytail.position.set(0.4, 2.1, -0.2);
            rightPonytail.scale.set(1, 2, 1);
            group.add(rightPonytail);

            // 手臂
            const armGeometry = new THREE.CapsuleGeometry(0.15, 1.2, 6, 12);
            const armMaterial = new THREE.MeshLambertMaterial({ color: skinColor });
            
            const leftArm = new THREE.Mesh(armGeometry, armMaterial);
            leftArm.position.set(-0.6, 1.3, 0);
            leftArm.rotation.z = Math.PI / 8;
            group.add(leftArm);
            
            const rightArm = new THREE.Mesh(armGeometry, armMaterial);
            rightArm.position.set(0.6, 1.3, 0);
            rightArm.rotation.z = -Math.PI / 8;
            group.add(rightArm);

            // 腿部
            const legGeometry = new THREE.CapsuleGeometry(0.18, 1.4, 6, 12);
            const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2D1B69 });
            
            const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
            leftLeg.position.set(-0.2, -0.3, 0);
            group.add(leftLeg);
            
            const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
            rightLeg.position.set(0.2, -0.3, 0);
            group.add(rightLeg);

            // 胸前宝石
            const gemGeometry = new THREE.SphereGeometry(0.08, 8, 8);
            const gemMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 });
            const chestGem = new THREE.Mesh(gemGeometry, gemMaterial);
            chestGem.position.set(0, 1.4, 0.4);
            group.add(chestGem);

            // 添加到场景
            group.position.y = -1;
            group.scale.setScalar(0.8);
            scene.add(group);

            // 添加简单的动画
            function animate() {
                const time = Date.now() * 0.001;
                group.position.y = Math.sin(time * 2) * 0.1 - 1;
                group.rotation.y = Math.sin(time * 1.5) * 0.1;
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            renderer.render(scene, camera);
        }
        animate();

        // 处理窗口大小变化
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
</body>
</html>