<!DOCTYPE html>
<html>
<head>
    <title>刻晴PMX + VMD测试</title>
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
        }
        canvas { display: block; }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-family: Arial;
            z-index: 100;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            max-width: 300px;
        }
        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            background: #6B46C1;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #8B5CF6;
        }
    </style>
</head>
<body>
    <div id="info">正在加载刻晴模型...</div>
    <div id="controls">
        <button onclick="playDance()">播放舞蹈</button>
        <button onclick="playWalk()">播放走路</button>
        <button onclick="playIdle()">播放待机</button>
    </div>
    <script type="module">
        import * as THREE from './node_modules/three/build/three.module.js';
        import { MMDLoader } from './node_modules/three/examples/jsm/loaders/MMDLoader.js';
        import { MMDAnimationHelper } from './node_modules/three/examples/jsm/animation/MMDAnimationHelper.js';
        import { OrbitControls } from './node_modules/three/examples/jsm/controls/OrbitControls.js';

        // 全局变量
        let scene, camera, renderer, controls;
        let model, helper, mixer;
        let clock = new THREE.Clock();

        // 初始化场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x222244);

            // 创建相机
            camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 2000);
            camera.position.set(0, 10, 30);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.body.appendChild(renderer.domElement);

            // 添加控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.target.set(0, 10, 0);
            controls.update();

            // 添加光源
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(-1, 1, 1);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 添加地面
            const groundGeometry = new THREE.PlaneGeometry(100, 100);
            const groundMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x999999, 
                transparent: true, 
                opacity: 0.3 
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);

            // 加载模型
            loadModel();
        }

        // 加载刻晴模型
        function loadModel() {
            const loader = new MMDLoader();
            document.getElementById('info').textContent = '正在加载刻晴PMX模型...';

            loader.load(
                './assets/models/刻晴.pmx',
                (mmd) => {
                    console.log('🎉 刻晴模型加载成功!', mmd);
                    model = mmd;
                    model.position.y = 0;
                    model.castShadow = true;
                    scene.add(model);

                    // 创建动画助手
                    helper = new MMDAnimationHelper();

                    document.getElementById('info').innerHTML = `
                        ✅ 刻晴模型加载成功！<br>
                        📊 网格数量: ${countMeshes(model)}<br>
                        🎭 材质数量: ${countMaterials(model)}<br>
                        🦴 骨骼数量: ${model.skeleton ? model.skeleton.bones.length : 0}<br>
                        😊 表情数量: ${model.morphTargetInfluences ? model.morphTargetInfluences.length : 0}
                    `;

                    // 加载VMD动画
                    loadVMDAnimation();
                },
                (progress) => {
                    if (progress.lengthComputable) {
                        const percent = Math.round((progress.loaded / progress.total) * 100);
                        document.getElementById('info').textContent = `加载刻晴模型: ${percent}%`;
                    }
                },
                (error) => {
                    console.error('❌ 刻晴模型加载失败:', error);
                    document.getElementById('info').innerHTML = `
                        ❌ 刻晴模型加载失败: ${error.message}<br>
                        🔍 请检查文件路径: ./assets/models/刻晴.pmx<br>
                        📁 确保文件存在且格式正确
                    `;
                    // 创建占位符模型
                    createPlaceholder();
                }
            );
        }

        // 加载VMD动画
        function loadVMDAnimation() {
            if (!model) return;

            const loader = new MMDLoader();
            document.getElementById('info').innerHTML += '<br>🎬 正在加载VMD动画...';

            loader.loadAnimation(
                './assets/models/杂鱼 杂鱼（绝区零）.vmd',
                model,
                (vmd) => {
                    console.log('🎉 VMD动画加载成功!', vmd);
                    
                    // 添加动画到助手
                    helper.add(model, {
                        animation: vmd,
                        physics: false
                    });

                    mixer = helper.objects.get(model).mixer;
                    document.getElementById('info').innerHTML += '<br>✅ VMD动画加载成功！';

                    // 自动播放待机动画
                    playIdle();
                },
                (progress) => {
                    console.log('VMD加载进度:', progress);
                },
                (error) => {
                    console.error('❌ VMD动画加载失败:', error);
                    document.getElementById('info').innerHTML += `<br>⚠️ VMD动画加载失败: ${error.message}`;
                }
            );
        }

        // 创建占位符模型
        function createPlaceholder() {
            const group = new THREE.Group();
            
            // 刻晴风格的占位符
            const keqingPurple = 0x6B46C1;
            const skinColor = 0xFFDBB5;
            const hairColor = 0x4C1D95;

            // 身体
            const bodyGeometry = new THREE.CapsuleGeometry(2, 8, 8, 16);
            const bodyMaterial = new THREE.MeshLambertMaterial({ color: keqingPurple });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.y = 8;
            body.castShadow = true;
            group.add(body);

            // 头部
            const headGeometry = new THREE.SphereGeometry(1.5, 16, 16);
            const headMaterial = new THREE.MeshLambertMaterial({ color: skinColor });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.y = 14;
            head.castShadow = true;
            group.add(head);

            // 头发
            const hairGeometry = new THREE.SphereGeometry(1.6, 16, 16);
            const hairMaterial = new THREE.MeshLambertMaterial({ 
                color: hairColor, 
                transparent: true, 
                opacity: 0.8 
            });
            const hair = new THREE.Mesh(hairGeometry, hairMaterial);
            hair.position.y = 14.5;
            hair.scale.set(1, 0.8, 1);
            hair.castShadow = true;
            group.add(hair);

            // 双马尾
            const ponytailGeometry = new THREE.SphereGeometry(0.6, 8, 8);
            const ponytailMaterial = new THREE.MeshLambertMaterial({ color: hairColor });
            
            const leftPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
            leftPonytail.position.set(-1.8, 13, -1);
            leftPonytail.scale.set(1, 3, 1);
            leftPonytail.castShadow = true;
            group.add(leftPonytail);
            
            const rightPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
            rightPonytail.position.set(1.8, 13, -1);
            rightPonytail.scale.set(1, 3, 1);
            rightPonytail.castShadow = true;
            group.add(rightPonytail);

            model = group;
            scene.add(model);

            // 添加简单动画
            function animatePlaceholder() {
                if (model) {
                    const time = Date.now() * 0.001;
                    model.position.y = Math.sin(time * 2) * 0.5;
                    model.rotation.y = Math.sin(time * 1.5) * 0.1;
                }
                requestAnimationFrame(animatePlaceholder);
            }
            animatePlaceholder();
        }

        // 动画控制函数
        window.playDance = function() {
            if (mixer) {
                mixer.stopAllAction();
                const action = mixer.clipAction(mixer._clip);
                action.setLoop(THREE.LoopRepeat);
                action.play();
                console.log('🕺 播放舞蹈动画');
            }
        };

        window.playWalk = function() {
            if (mixer) {
                mixer.stopAllAction();
                const action = mixer.clipAction(mixer._clip);
                action.setLoop(THREE.LoopRepeat);
                action.timeScale = 0.5; // 慢一点
                action.play();
                console.log('🚶 播放走路动画');
            }
        };

        window.playIdle = function() {
            if (mixer) {
                mixer.stopAllAction();
                const action = mixer.clipAction(mixer._clip);
                action.setLoop(THREE.LoopRepeat);
                action.timeScale = 0.3; // 很慢的待机
                action.play();
                console.log('😌 播放待机动画');
            }
        };

        // 工具函数
        function countMeshes(object) {
            let count = 0;
            object.traverse((child) => {
                if (child.isMesh) count++;
            });
            return count;
        }

        function countMaterials(object) {
            const materials = new Set();
            object.traverse((child) => {
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => materials.add(mat));
                    } else {
                        materials.add(child.material);
                    }
                }
            });
            return materials.size;
        }

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            
            const delta = clock.getDelta();
            if (helper) {
                helper.update(delta);
            }
            
            controls.update();
            renderer.render(scene, camera);
        }

        // 处理窗口大小变化
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // 启动应用
        init();
        animate();
    </script>
</body>
</html>