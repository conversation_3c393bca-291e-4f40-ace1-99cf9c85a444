// 应用配置类型
export interface AppConfig {
  display: DisplayConfig;
  audio: AudioConfig;
  ai: AIConfig;
  behavior: BehaviorConfig;
  system: SystemConfig;
}

export interface DisplayConfig {
  modelScale: number;
  animationSpeed: number;
  position: { x: number; y: number };
  alwaysOnTop: boolean;
}

export interface AudioConfig {
  volume: number;
  voiceProfile: string;
  enableVoiceChat: boolean;
  microphoneDevice: string;
}

export interface AIConfig {
  doubaoEndpoint: string;
  doubaoModel: string;
  systemPrompt: string;
  conversationHistory: boolean;
  maxHistoryLength: number;
}

export interface BehaviorConfig {
  idleAnimationInterval: number;
  autoResponse: boolean;
  clickToTalk: boolean;
}

export interface SystemConfig {
  autoStart: boolean;
  minimizeToTray: boolean;
  showNotifications: boolean;
}

// 3D模型相关类型
export interface MMDModel {
  mesh: any; // THREE.Mesh
  skeleton: any; // THREE.Skeleton
  animations: any[]; // THREE.AnimationClip[]
  materials: any[]; // THREE.Material[]
  textures: any[]; // THREE.Texture[]
}

export interface ModelAssets {
  modelFile: string;
  textureFiles: string[];
  animationFiles: string[];
}

export enum AnimationType {
  IDLE = 'idle',
  TALKING = 'talking',
  HAPPY = 'happy',
  SURPRISED = 'surprised',
  THINKING = 'thinking'
}

// 语音相关类型
export interface VoiceProfile {
  id: string;
  name: string;
  modelPath: string;
  sampleRate: number;
  characteristics: VoiceCharacteristics;
}

export interface VoiceCharacteristics {
  pitch: number;
  speed: number;
  emotion: string;
}

// AI对话相关类型
export interface CharacterProfile {
  name: string;
  personality: string[];
  speechPatterns: string[];
  knowledgeBase: string[];
  responseTemplates: ResponseTemplate[];
  doubaoSystemPrompt: string;
}

export interface ResponseTemplate {
  trigger: string;
  responses: string[];
  animation?: AnimationType;
}

export interface ConversationContext {
  history: ChatMessage[];
  currentEmotion: string;
  userPreferences: Record<string, any>;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  emotion?: string;
}

// 错误处理类型
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface AppError {
  code: string;
  message: string;
  severity: ErrorSeverity;
  timestamp: number;
  context?: Record<string, any>;
}

// IPC通信类型
export interface IPCMessage {
  type: string;
  payload?: any;
  requestId?: string;
}

// 事件类型
export type AppEventType = 
  | 'model-loaded'
  | 'animation-changed'
  | 'voice-recording-started'
  | 'voice-recording-stopped'
  | 'ai-response-received'
  | 'settings-changed'
  | 'error-occurred';