// Jest测试环境设置
import '@testing-library/jest-dom';

// Mock Electron APIs
const mockElectronAPI = {
  minimizeWindow: jest.fn(),
  closeWindow: jest.fn(),
  showTrayMenu: jest.fn(),
  getSettings: jest.fn(),
  saveSettings: jest.fn(),
  selectModelFile: jest.fn(),
  selectAudioFile: jest.fn(),
  onSettingsChanged: jest.fn(),
  removeAllListeners: jest.fn(),
};

// 设置全局window.electronAPI mock
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

// Mock Three.js WebGL context
const mockWebGLContext = {
  getExtension: jest.fn(),
  getParameter: jest.fn(),
  createShader: jest.fn(),
  shaderSource: jest.fn(),
  compileShader: jest.fn(),
  createProgram: jest.fn(),
  attachShader: jest.fn(),
  linkProgram: jest.fn(),
  useProgram: jest.fn(),
  // 添加更多WebGL方法...
};

// Mock HTMLCanvasElement.getContext
HTMLCanvasElement.prototype.getContext = jest.fn((contextId: string) => {
  if (contextId === 'webgl' || contextId === 'webgl2') {
    return mockWebGLContext;
  }
  return null;
}) as any;

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn();