import React, { useEffect, useRef } from 'react';
import * as THREE from 'three';

const SimpleAnimationTest: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const cubeRef = useRef<THREE.Mesh | null>(null);
  const mixerRef = useRef<THREE.AnimationMixer | null>(null);
  const clockRef = useRef<THREE.Clock>(new THREE.Clock());

  useEffect(() => {
    if (!mountRef.current) return;

    console.log('🧪 开始简单动画测试');

    // 创建场景
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(75, 400 / 300, 0.1, 1000);
    camera.position.z = 5;
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ alpha: true });
    renderer.setSize(400, 300);
    renderer.setClearColor(0x000000, 0);
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // 创建一个立方体
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
    const cube = new THREE.Mesh(geometry, material);
    scene.add(cube);
    cubeRef.current = cube;

    // 创建动画
    const times = [0, 1, 2];
    const positionValues = [
      0, 0, 0,    // 起始位置
      0, 1, 0,    // 上升
      0, 0, 0     // 回到起始位置
    ];
    const rotationValues = [
      0, 0, 0, 1,    // 起始旋转
      0, 0, 0.707, 0.707,  // 旋转90度
      0, 0, 1, 0     // 旋转180度
    ];

    const positionTrack = new THREE.VectorKeyframeTrack('.position', times, positionValues);
    const rotationTrack = new THREE.QuaternionKeyframeTrack('.quaternion', times, rotationValues);

    const clip = new THREE.AnimationClip('test', 2, [positionTrack, rotationTrack]);
    
    // 创建动画混合器
    const mixer = new THREE.AnimationMixer(cube);
    mixerRef.current = mixer;

    const action = mixer.clipAction(clip);
    action.setLoop(THREE.LoopRepeat, Infinity);
    action.play();

    console.log('✅ 动画创建完成，开始播放');

    // 渲染循环
    const animate = () => {
      requestAnimationFrame(animate);

      const delta = clockRef.current.getDelta();
      if (mixerRef.current) {
        mixerRef.current.update(delta);
      }

      renderer.render(scene, camera);
    };

    animate();

    // 清理函数
    return () => {
      console.log('🧹 清理简单动画测试');
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
      geometry.dispose();
      material.dispose();
    };
  }, []);

  return (
    <div style={{ padding: 20 }}>
      <h3>简单动画测试</h3>
      <p>如果动画系统工作正常，你应该看到一个绿色立方体在上下移动并旋转。</p>
      <div 
        ref={mountRef} 
        style={{ 
          border: '1px solid #ccc', 
          width: 400, 
          height: 300,
          backgroundColor: 'rgba(0,0,0,0.1)'
        }} 
      />
    </div>
  );
};

export default SimpleAnimationTest;
