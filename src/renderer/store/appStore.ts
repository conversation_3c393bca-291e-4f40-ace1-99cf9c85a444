import { create } from 'zustand';

interface AppState {
  // 应用状态
  isLoading: boolean;
  error: string | null;
  
  // 3D模型状态
  modelLoaded: boolean;
  currentAnimation: string;
  
  // 语音状态
  isRecording: boolean;
  isSpeaking: boolean;
  
  // 设置状态
  settings: {
    volume: number;
    animationSpeed: number;
    autoStart: boolean;
    modelScale: number;
    position: { x: number; y: number };
  };
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setModelLoaded: (loaded: boolean) => void;
  setCurrentAnimation: (animation: string) => void;
  setRecording: (recording: boolean) => void;
  setSpeaking: (speaking: boolean) => void;
  updateSettings: (settings: Partial<AppState['settings']>) => void;
}

export const useAppStore = create<AppState>((set) => ({
  // 初始状态
  isLoading: true,
  error: null,
  modelLoaded: false,
  currentAnimation: 'idle',
  isRecording: false,
  isSpeaking: false,
  settings: {
    volume: 0.8,
    animationSpeed: 1.0,
    autoStart: true,
    modelScale: 1.0,
    position: { x: 0, y: 0 },
  },
  
  // Actions
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  setModelLoaded: (loaded) => set({ modelLoaded: loaded }),
  setCurrentAnimation: (animation) => set({ currentAnimation: animation }),
  setRecording: (recording) => set({ isRecording: recording }),
  setSpeaking: (speaking) => set({ isSpeaking: speaking }),
  updateSettings: (newSettings) => 
    set((state) => ({
      settings: { ...state.settings, ...newSettings }
    })),
}));