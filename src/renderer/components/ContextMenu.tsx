import React from 'react';

interface ContextMenuProps {
  position: { x: number; y: number };
  onClose: () => void;
}

const ContextMenu: React.FC<ContextMenuProps> = ({ position, onClose }) => {
  const handleItemClick = (action: string) => {
    console.log(`菜单项被点击: ${action}`);
    
    switch (action) {
      case 'settings':
        // TODO: 打开设置窗口
        break;
      case 'hide':
        // TODO: 隐藏窗口
        break;
      case 'exit':
        if (window.electronAPI) {
          window.electronAPI.quitApp();
        }
        break;
    }
    
    onClose();
  };

  return (
    <div 
      className="context-menu"
      style={{
        left: position.x,
        top: position.y,
      }}
    >
      <div 
        className="context-menu-item"
        onClick={() => handleItemClick('settings')}
      >
        ⚙️ 设置
      </div>
      
      <div className="context-menu-separator" />
      
      <div 
        className="context-menu-item"
        onClick={() => handleItemClick('hide')}
      >
        👁️ 隐藏
      </div>
      
      <div 
        className="context-menu-item"
        onClick={() => handleItemClick('exit')}
      >
        ❌ 退出
      </div>
    </div>
  );
};

export default ContextMenu;