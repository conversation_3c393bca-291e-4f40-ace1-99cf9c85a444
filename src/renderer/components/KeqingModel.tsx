import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';
import { useAppStore } from '../store/appStore';
import { ThreeEngine } from '../engine/ThreeEngine';
import { MMDModelManager } from '../engine/MMDModelManager';
import { MMDModelData } from '../engine/MMDLoader';
import { AnimationSystem, AnimationType } from '../engine/AnimationSystem';
import { AnimationStateManager } from '../engine/AnimationStateManager';
import { VMDLoader } from '../engine/VMDLoader';
import { AnimationDebug } from '../debug/AnimationDebug';
// @ts-ignore - MMDLoader没有类型声明
import { MMDLoader } from 'three/examples/jsm/loaders/MMDLoader.js';
// @ts-ignore - MMDAnimationHelper没有类型声明
import { MMDAnimationHelper } from 'three/examples/jsm/animation/MMDAnimationHelper.js';

const KeqingModel: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const engineRef = useRef<ThreeEngine | null>(null);
  const modelManagerRef = useRef<MMDModelManager | null>(null);
  const currentModelRef = useRef<MMDModelData | null>(null);
  const animationSystemRef = useRef<AnimationSystem | null>(null);
  const stateManagerRef = useRef<AnimationStateManager | null>(null);
  const vmdLoaderRef = useRef<VMDLoader | null>(null);
  
  const { setLoading, setModelLoaded, setError } = useAppStore();

  useEffect(() => {
    if (!mountRef.current) return;

    // 检查WebGL支持
    const webglSupport = ThreeEngine.checkWebGLSupport();
    if (!webglSupport.supported) {
      setError(`WebGL不受支持: ${webglSupport.error}`);
      setLoading(false);
      return;
    }

    const initializeScene = async () => {
      try {
        // 初始化3D引擎
        const engine = new ThreeEngine(
          mountRef.current!,
          {
            antialias: true,
            alpha: true,
            powerPreference: 'high-performance'
          },
          {
            fov: 50, // 稍微增大视野角度
            position: { x: 0, y: 8, z: 35 } // 提高相机Y位置，让头部更清晰
          },
          {
            ambient: { color: 0xffffff, intensity: 0.6 },
            directional: { 
              color: 0xffffff, 
              intensity: 0.8,
              position: { x: 1, y: 1, z: 1 }
            }
          }
        );

        engineRef.current = engine;

        // 设置错误处理回调
        engine.setOnError((error) => {
          console.error('3D引擎错误:', error);
          setError(`3D渲染错误: ${error.message}`);
        });

        // 设置性能监控回调
        engine.setOnRender((stats) => {
          if (stats.fps < 30) {
            console.warn('FPS过低:', stats.fps);
          }
        });

        // 初始化MMD模型管理器
        const modelManager = new MMDModelManager({
          maxCacheSize: 3,
          enablePreloading: true,
          retryAttempts: 2,
          retryDelay: 1000
        });

        modelManagerRef.current = modelManager;

        // 初始化动画系统
        const animationSystem = new AnimationSystem();
        animationSystemRef.current = animationSystem;

        // 初始化状态管理器
        const stateManager = new AnimationStateManager();
        stateManagerRef.current = stateManager;

        // 初始化VMD加载器
        const vmdLoader = new VMDLoader();
        vmdLoaderRef.current = vmdLoader;

        // 设置动画系统回调
        animationSystem.setCallbacks({
          onAnimationStart: (type) => {
            console.log('动画开始:', type);
          },
          onAnimationEnd: (type) => {
            console.log('动画结束:', type);
            stateManager.recordActivity();
          },
          onAnimationLoop: (type) => {
            console.log('动画循环:', type);
          },
          onTransitionStart: (from, to) => {
            console.log('动画过渡开始:', from, '->', to);
          },
          onTransitionEnd: (from, to) => {
            console.log('动画过渡结束:', from, '->', to);
          }
        });

        // 设置状态管理器回调
        stateManager.setOnStateChange((event) => {
          console.log('动画状态变化:', event.currentState.type, event.currentState.state);
        });

        // 设置模型加载回调
        modelManager.setLoadCallbacks({
          onLoadStart: (url) => {
            console.log('开始加载模型:', url);
            setLoading(true);
          },
          onLoadProgress: (url, progress) => {
            console.log(`模型加载进度: ${url} - ${progress}%`);
          },
          onLoadComplete: (url, model) => {
            console.log('模型加载完成:', url);
            setLoading(false);
            setModelLoaded(true);
          },
          onLoadError: (url, error) => {
            console.error('模型加载失败:', url, error);
            setError(`模型加载失败: ${error}`);
            setLoading(false);
          }
        });

        // 添加地面平面作为参考
        const groundGeometry = new THREE.PlaneGeometry(10, 10);
        const groundMaterial = new THREE.MeshLambertMaterial({
          color: 0x404040,
          transparent: true,
          opacity: 0.1
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -3; // 调整地面位置
        ground.receiveShadow = true;
        engine.addToScene(ground);

        // 开始渲染
        engine.startRendering();

        // 加载刻晴模型
        await loadKeqingModel(engine, modelManager, animationSystem, stateManager);

      } catch (error) {
        console.error('场景初始化失败:', error);
        setError(`场景初始化失败: ${(error as Error).message}`);
        setLoading(false);
      }
    };

    const loadVMDAnimations = async (
      animationSystem: AnimationSystem,
      vmdLoader: VMDLoader
    ) => {
      try {
        console.log('🎬 开始加载VMD动画文件...');
        
        // 加载杂鱼VMD动画文件
        const vmdPath = './assets/models/杂鱼 杂鱼（绝区零）.vmd';
        
        // 直接使用VMDLoader加载VMD文件
        const vmdData = await vmdLoader.loadVMD(vmdPath, {
          frameRate: 30,
          enableBoneAnimation: true,
          enableMorphAnimation: true
        });
        
        console.log('✅ VMD数据加载成功:', {
          bones: vmdData.bones.length,
          morphs: vmdData.morphs.length,
          duration: vmdData.duration
        });
        
        // 将VMD数据转换为Three.js动画剪辑
        const danceClip = vmdLoader.convertToAnimationClip(vmdData, 'dance_animation');
        const walkClip = vmdLoader.convertToAnimationClip(vmdData, 'walk_animation');
        
        console.log('🎭 动画剪辑创建成功');
        
        // 使用AnimationSystem的loadVMDAnimation方法加载动画
        const danceSuccess = await animationSystem.loadVMDAnimation(
          vmdPath, 
          AnimationType.DANCING,
          {
            duration: vmdData.duration,
            loop: true,
            priority: 5
          }
        );
        
        const walkSuccess = await animationSystem.loadVMDAnimation(
          vmdPath,
          AnimationType.WALKING,
          {
            duration: vmdData.duration,
            loop: true,
            priority: 3
          }
        );
        
        if (danceSuccess && walkSuccess) {
          console.log('🎉 VMD动画集成成功！可以通过右键播放舞蹈，双击播放走路');
        } else {
          console.warn('⚠️ 部分VMD动画加载失败，将使用内置动画');
        }
        
      } catch (error) {
        console.error('❌ VMD动画加载过程出错:', error);
      }
    };

    const loadKeqingModel = async (
      engine: ThreeEngine, 
      modelManager: MMDModelManager,
      animationSystem: AnimationSystem,
      stateManager: AnimationStateManager
    ) => {
      try {
        // 尝试加载刻晴PMX模型
        console.log('🎭 开始加载刻晴PMX模型...');
        const modelPath = './assets/models/刻晴.pmx';
        console.log('📁 PMX文件路径:', modelPath);
        
        const result = await modelManager.loadModel(modelPath, {
          enablePhysics: false,
          enableAnimation: true,
          scale: 1.0, // 恢复原始大小
          position: { x: 0, y: -2, z: 0 } // 稍微向下移动，让头部更清晰
        });

        if (result.success && result.model) {
          // 移除之前的模型
          if (currentModelRef.current) {
            engine.removeFromScene(currentModelRef.current.model);
          }

          // 添加新模型到场景
          currentModelRef.current = result.model;
          engine.addToScene(result.model.model);

          // 初始化动画系统
          animationSystem.initialize(result.model.model);
          
          // 加载VMD动画文件
          await loadVMDAnimations(animationSystem, vmdLoaderRef.current!);
          
          // 开始播放待机动画
          animationSystem.playAnimation(AnimationType.IDLE);
          
          // 添加到状态管理器
          stateManager.addAnimationState(AnimationType.IDLE, {
            type: AnimationType.IDLE,
            duration: 2,
            loop: true,
            priority: 1
          });

          console.log('刻晴模型加载成功，动画系统已初始化');
        } else {
          // 加载失败，使用默认模型
          console.warn('PMX模型加载失败，使用默认模型');
          
          // 等待默认模型初始化完成
          let defaultModel = modelManager.getDefaultModel();
          if (!defaultModel) {
            console.log('等待默认模型初始化...');
            // 等待一段时间让默认模型初始化
            await new Promise(resolve => setTimeout(resolve, 500));
            defaultModel = modelManager.getDefaultModel();
          }
          
          if (defaultModel) {
            // 调整默认模型的位置和大小
            defaultModel.model.scale.setScalar(1.0);
            defaultModel.model.position.set(0, -2, 0);
            
            currentModelRef.current = defaultModel;
            engine.addToScene(defaultModel.model);

            // 初始化动画系统
            animationSystem.initialize(defaultModel.model);

            // 加载VMD动画文件
            await loadVMDAnimations(animationSystem, vmdLoaderRef.current!);

            // 开始播放待机动画
            animationSystem.playAnimation(AnimationType.IDLE);
            
            // 添加到状态管理器
            stateManager.addAnimationState(AnimationType.IDLE, {
              type: AnimationType.IDLE,
              duration: 2,
              loop: true,
              priority: 1
            });
            
            console.log('默认模型已添加到场景，动画系统已初始化');
          } else {
            console.error('无法获取默认模型');
            // 创建一个简单的立方体作为最后的备选
            const geometry = new THREE.BoxGeometry(1, 2, 1); // 调整尺寸
            const material = new THREE.MeshLambertMaterial({
              color: 0x9966ff,
              transparent: true,
              opacity: 0.8
            });
            const cube = new THREE.Mesh(geometry, material);
            cube.position.set(0, -1, 0); // 调整位置
            cube.castShadow = true;
            cube.receiveShadow = true;
            engine.addToScene(cube);

            // 创建一个简单的组来包装立方体，以便动画系统可以处理
            const cubeGroup = new THREE.Group();
            cubeGroup.add(cube);
            cubeGroup.position.set(0, -2, 0); // 调整组的位置
            engine.addToScene(cubeGroup);
            
            // 初始化动画系统
            animationSystem.initialize(cubeGroup);
            
            // 加载VMD动画文件
            await loadVMDAnimations(animationSystem, vmdLoaderRef.current!);
            
            // 播放待机动画
            animationSystem.playAnimation(AnimationType.IDLE);
            
            console.log('已添加备用立方体');
          }
        }
      } catch (error) {
        console.error('模型加载过程出错:', error);
        setError(`模型加载失败: ${(error as Error).message}`);
      }
    };

    // 注册动画更新回调到引擎
    const animationUpdateCallback = (deltaTime: number) => {
      if (animationSystemRef.current) {
        animationSystemRef.current.update();
      }
    };

    if (engineRef.current) {
      engineRef.current.addUpdateCallback(animationUpdateCallback);
    }

    initializeScene();

    // 清理函数
    return () => {
      // 移除动画更新回调
      if (engineRef.current) {
        engineRef.current.removeUpdateCallback(animationUpdateCallback);
      }

      if (animationSystemRef.current) {
        animationSystemRef.current.dispose();
        animationSystemRef.current = null;
      }
      if (stateManagerRef.current) {
        stateManagerRef.current.dispose();
        stateManagerRef.current = null;
      }
      if (vmdLoaderRef.current) {
        vmdLoaderRef.current.dispose();
        vmdLoaderRef.current = null;
      }
      if (modelManagerRef.current) {
        modelManagerRef.current.dispose();
        modelManagerRef.current = null;
      }
      if (engineRef.current) {
        engineRef.current.dispose();
        engineRef.current = null;
      }
      currentModelRef.current = null;
    };
  }, [setLoading, setModelLoaded, setError]);

  const handleClick = () => {
    console.log('刻晴被点击了！');
    
    // 记录用户活动
    if (stateManagerRef.current) {
      stateManagerRef.current.recordActivity();
    }
    
    // 随机播放不同的动画
    const animations = [AnimationType.HAPPY, AnimationType.SURPRISED, AnimationType.WAVING];
    const randomAnimation = animations[Math.floor(Math.random() * animations.length)];
    
    // 播放随机动画
    if (animationSystemRef.current) {
      animationSystemRef.current.playAnimation(randomAnimation);
    }
    
    // 添加到状态管理器
    if (stateManagerRef.current) {
      stateManagerRef.current.addAnimationState(randomAnimation, {
        type: randomAnimation,
        duration: 2,
        loop: false,
        priority: 4
      });
    }
    
    console.log('播放动画:', randomAnimation);
    // TODO: 触发语音对话
  };

  const handleRightClick = (event: React.MouseEvent) => {
    event.preventDefault();
    console.log('右键点击 - 播放特殊动画');
    
    // 记录用户活动
    if (stateManagerRef.current) {
      stateManagerRef.current.recordActivity();
    }
    
    // 播放VMD舞蹈动画
    if (animationSystemRef.current) {
      if (animationSystemRef.current.hasAnimation(AnimationType.DANCING)) {
        animationSystemRef.current.playAnimation(AnimationType.DANCING);
        
        // 添加到状态管理器
        if (stateManagerRef.current) {
          stateManagerRef.current.addAnimationState(AnimationType.DANCING, {
            type: AnimationType.DANCING,
            duration: 8,
            loop: true,
            priority: 5
          });
        }
        
        console.log('播放VMD舞蹈动画！');
      } else {
        console.log('VMD舞蹈动画未加载，播放挥手动画');
        animationSystemRef.current.playAnimation(AnimationType.WAVING);
      }
    }
  };

  const handleDoubleClick = () => {
    console.log('双击 - 播放走路动画');
    
    // 记录用户活动
    if (stateManagerRef.current) {
      stateManagerRef.current.recordActivity();
    }
    
    // 播放走路动画
    if (animationSystemRef.current) {
      if (animationSystemRef.current.hasAnimation(AnimationType.WALKING)) {
        animationSystemRef.current.playAnimation(AnimationType.WALKING);
        
        // 添加到状态管理器
        if (stateManagerRef.current) {
          stateManagerRef.current.addAnimationState(AnimationType.WALKING, {
            type: AnimationType.WALKING,
            duration: 4,
            loop: true,
            priority: 3
          });
        }
        
        console.log('播放VMD走路动画！');
      } else {
        console.log('VMD走路动画未加载，播放思考动画');
        animationSystemRef.current.playAnimation(AnimationType.THINKING);
      }
    }
  };

  return (
    <>
      <div
        ref={mountRef}
        className="keqing-model-container"
        onClick={handleClick}
        onContextMenu={handleRightClick}
        onDoubleClick={handleDoubleClick}
        style={{ cursor: 'pointer' }}
        title="左键：随机动画 | 右键：舞蹈动画 | 双击：走路动画"
      />
      <AnimationDebug animationSystem={animationSystemRef.current} />
    </>
  );
};

export default KeqingModel;