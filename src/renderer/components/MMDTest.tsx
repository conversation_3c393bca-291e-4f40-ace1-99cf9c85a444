import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';

const MMDTest: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!mountRef.current) return;

    console.log('🎭 开始MMD加载测试...');

    let scene: THREE.Scene;
    let camera: THREE.PerspectiveCamera;
    let renderer: THREE.WebGLRenderer;
    let animationId: number;

    const init = async () => {
      try {
        // 创建基本场景
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x222244);

        camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 2000);
        camera.position.set(0, 10, 30);

        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        mountRef.current!.appendChild(renderer.domElement);

        // 添加光源
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(-1, 1, 1);
        directionalLight.castShadow = true;
        scene.add(directionalLight);

        // 添加地面
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
          color: 0x999999, 
          transparent: true, 
          opacity: 0.3 
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        scene.add(ground);

        console.log('✅ 基础场景创建成功');

        // 尝试动态导入MMDLoader
        try {
          console.log('📥 尝试导入MMDLoader...');
          
          // 动态导入MMDLoader
          // @ts-ignore - MMDLoader没有类型声明
          const { MMDLoader } = await import('three/examples/jsm/loaders/MMDLoader.js');
          console.log('✅ MMDLoader导入成功!', MMDLoader);

          const loader = new MMDLoader();
          console.log('✅ MMDLoader实例创建成功');

          // 尝试加载PMX文件
          console.log('📁 尝试加载PMX文件: ./assets/models/刻晴.pmx');
          updateInfo('📁 正在加载PMX文件...');
          
          // 先检查文件是否存在
          try {
            const response = await fetch('./assets/models/刻晴.pmx', { method: 'HEAD' });
            console.log('📋 文件检查结果:', response.status, response.statusText);
            if (!response.ok) {
              throw new Error(`文件不存在或无法访问 (状态: ${response.status})`);
            }
            console.log('✅ PMX文件存在，开始加载...');
          } catch (fetchError) {
            console.error('❌ PMX文件检查失败:', fetchError);
            updateInfo(`❌ PMX文件不存在: ${(fetchError as Error).message}`);
            createPlaceholder();
            return;
          }
          
          loader.load(
            './assets/models/刻晴.pmx',
            (mmd: any) => {
              console.log('🎉 PMX模型加载成功!', mmd);
              console.log('模型详细信息:', {
                name: mmd.name,
                children: mmd.children?.length,
                geometry: mmd.geometry ? '有几何体' : '无几何体',
                material: mmd.material ? '有材质' : '无材质',
                skeleton: mmd.skeleton ? '有骨骼' : '无骨骼'
              });
              
              mmd.position.y = 0;
              scene.add(mmd);
              
              // 显示成功信息
              updateInfo('✅ 刻晴PMX模型加载成功！');
            },
            (progress: any) => {
              if (progress.lengthComputable) {
                const percent = Math.round((progress.loaded / progress.total) * 100);
                console.log(`📥 PMX加载进度: ${percent}% (${progress.loaded}/${progress.total} bytes)`);
                updateInfo(`📥 加载刻晴模型: ${percent}%`);
              } else {
                console.log('📥 PMX加载进度:', progress.loaded, 'bytes');
                updateInfo(`📥 加载中... ${progress.loaded} bytes`);
              }
            },
            (error: any) => {
              console.error('❌ PMX加载失败详情:', {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
              });
              updateInfo(`❌ PMX加载失败: ${error.message}`);
              
              // 创建占位符模型
              createPlaceholder();
            }
          );

        } catch (importError) {
          console.error('❌ MMDLoader导入失败:', importError);
          updateInfo(`❌ MMDLoader导入失败: ${(importError as Error).message}`);
          
          // 创建占位符模型
          createPlaceholder();
        }

        // 开始渲染循环
        const animate = () => {
          animationId = requestAnimationFrame(animate);
          renderer.render(scene, camera);
        };
        animate();

      } catch (error) {
        console.error('❌ 初始化失败:', error);
        updateInfo(`❌ 初始化失败: ${(error as Error).message}`);
      }
    };

    const createPlaceholder = () => {
      console.log('🎭 创建刻晴风格占位符...');
      
      const group = new THREE.Group();
      
      // 刻晴的紫色主题色
      const keqingPurple = 0x6B46C1;
      const skinColor = 0xFFDBB5;
      const hairColor = 0x4C1D95;

      // 身体
      const bodyGeometry = new THREE.CapsuleGeometry(2, 8, 8, 16);
      const bodyMaterial = new THREE.MeshLambertMaterial({ color: keqingPurple });
      const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
      body.position.y = 8;
      body.castShadow = true;
      group.add(body);

      // 头部
      const headGeometry = new THREE.SphereGeometry(1.5, 16, 16);
      const headMaterial = new THREE.MeshLambertMaterial({ color: skinColor });
      const head = new THREE.Mesh(headGeometry, headMaterial);
      head.position.y = 14;
      head.castShadow = true;
      group.add(head);

      // 头发
      const hairGeometry = new THREE.SphereGeometry(1.6, 16, 16);
      const hairMaterial = new THREE.MeshLambertMaterial({ 
        color: hairColor, 
        transparent: true, 
        opacity: 0.8 
      });
      const hair = new THREE.Mesh(hairGeometry, hairMaterial);
      hair.position.y = 14.5;
      hair.scale.set(1, 0.8, 1);
      hair.castShadow = true;
      group.add(hair);

      // 双马尾
      const ponytailGeometry = new THREE.SphereGeometry(0.6, 8, 8);
      const ponytailMaterial = new THREE.MeshLambertMaterial({ color: hairColor });
      
      const leftPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
      leftPonytail.position.set(-1.8, 13, -1);
      leftPonytail.scale.set(1, 3, 1);
      leftPonytail.castShadow = true;
      group.add(leftPonytail);
      
      const rightPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
      rightPonytail.position.set(1.8, 13, -1);
      rightPonytail.scale.set(1, 3, 1);
      rightPonytail.castShadow = true;
      group.add(rightPonytail);

      scene.add(group);
      
      // 添加简单动画
      const animatePlaceholder = () => {
        const time = Date.now() * 0.001;
        group.position.y = Math.sin(time * 2) * 0.5;
        group.rotation.y = Math.sin(time * 1.5) * 0.1;
        requestAnimationFrame(animatePlaceholder);
      };
      animatePlaceholder();
      
      updateInfo('🎭 刻晴风格占位符模型已创建');
    };

    const updateInfo = (message: string) => {
      const infoElement = document.getElementById('mmd-test-info');
      if (infoElement) {
        infoElement.innerHTML = message;
      }
    };

    init();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      if (renderer && mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
        renderer.dispose();
      }
    };
  }, []);

  return (
    <div>
      <div id="mmd-test-info" style={{ 
        position: 'absolute', 
        top: 10, 
        left: 10, 
        color: 'white', 
        zIndex: 100,
        background: 'rgba(0,0,0,0.7)',
        padding: '10px',
        borderRadius: '5px',
        maxWidth: '300px'
      }}>
        🎭 MMD加载测试初始化中...
      </div>
      <div ref={mountRef} />
    </div>
  );
};

export default MMDTest;