import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
// @ts-ignore - MMDLoader没有类型声明
import { MMDLoader } from 'three/examples/jsm/loaders/MMDLoader.js';
// @ts-ignore - MMDAnimationHelper没有类型声明
import { MMDAnimationHelper } from 'three/examples/jsm/animation/MMDAnimationHelper.js';
// @ts-ignore - OrbitControls没有类型声明
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

const StaticMMDTest: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const [status, setStatus] = useState('初始化中...');

  useEffect(() => {
    if (!mountRef.current) return;

    console.log('🎭 开始静态MMD测试...');
    setStatus('🎭 开始静态MMD测试...');

    let scene: THREE.Scene;
    let camera: THREE.PerspectiveCamera;
    let renderer: THREE.WebGLRenderer;
    let animationId: number;
    let helper: any;
    let clock: THREE.Clock;
    let controls: any;

    const init = () => {
      try {
        console.log('📦 创建Three.js场景...');
        setStatus('📦 创建Three.js场景...');

        // 创建基本场景
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x222244);

        camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 1, 2000);
        camera.position.set(0, 10, 30); // 参考BasicMMDTest的相机位置

        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        mountRef.current!.appendChild(renderer.domElement);

        // 添加光源
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(-1, 1, 1);
        directionalLight.castShadow = true;
        scene.add(directionalLight);

        // 添加地面
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
          color: 0x999999, 
          transparent: true, 
          opacity: 0.3 
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        scene.add(ground);

        console.log('✅ 基础场景创建成功');
        setStatus('✅ 基础场景创建成功');

        // 创建时钟
        clock = new THREE.Clock();

        // 创建动画助手
        helper = new MMDAnimationHelper();
        console.log('✅ MMDAnimationHelper创建成功');

        // 添加相机控制
        controls = new OrbitControls(camera, renderer.domElement);
        controls.target.set(0, 10, 0); // 设置相机焦点到模型中心
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.screenSpacePanning = false;
        controls.minDistance = 20;
        controls.maxDistance = 100;
        controls.maxPolarAngle = Math.PI / 2;
        console.log('✅ 相机控制器创建成功');

        // 开始渲染循环
        const animate = () => {
          animationId = requestAnimationFrame(animate);
          const delta = clock.getDelta();
          if (helper) {
            helper.update(delta);
          }
          if (controls) {
            controls.update();
          }
          renderer.render(scene, camera);
        };
        animate();

        console.log('🎬 渲染循环已启动');
        setStatus('🎬 渲染循环已启动');

        // 尝试加载PMX模型
        loadPMXModel();

      } catch (error) {
        console.error('❌ 初始化失败:', error);
        setStatus(`❌ 初始化失败: ${(error as Error).message}`);
      }
    };

    const loadPMXModel = () => {
      try {
        console.log('📥 创建MMDLoader...');
        setStatus('📥 创建MMDLoader...');

        const loader = new MMDLoader();
        console.log('✅ MMDLoader创建成功');

        // 尝试多种路径格式加载PMX文件
        const possiblePaths = [
          './assets/models/刻晴.pmx',
          'assets/models/刻晴.pmx',
          '/assets/models/刻晴.pmx'
        ];
        
        const tryLoadPMX = (pathIndex: number) => {
          if (pathIndex >= possiblePaths.length) {
            console.error('❌ 所有路径都尝试失败，创建占位符模型');
            setStatus('❌ 所有路径都尝试失败，使用占位符模型');
            createKeqingPlaceholder();
            return;
          }
          
          const currentPath = possiblePaths[pathIndex];
          console.log(`📁 尝试路径 ${pathIndex + 1}/${possiblePaths.length}: ${currentPath}`);
          setStatus(`📁 尝试路径 ${pathIndex + 1}/${possiblePaths.length}: ${currentPath}`);

          loader.load(
            currentPath,
            (mmd: any) => {
              console.log('🎉 PMX模型加载成功!', mmd);
              console.log('模型详细信息:', {
                name: mmd.name || 'Unknown',
                children: mmd.children?.length || 0,
                geometry: mmd.geometry ? '有几何体' : '无几何体',
                material: mmd.material ? '有材质' : '无材质',
                skeleton: mmd.skeleton ? '有骨骼' : '无骨骼',
                morphTargetInfluences: mmd.morphTargetInfluences?.length || 0
              });

              // 设置模型位置和大小
              mmd.position.set(0, 0, 0);   // 参考BasicMMDTest的模型位置
              mmd.scale.setScalar(1);      // 参考BasicMMDTest的模型缩放

              // 启用阴影
              mmd.traverse((child: any) => {
                if (child.isMesh) {
                  child.castShadow = true;
                  child.receiveShadow = true;
                }
              });

              scene.add(mmd);
              
              setStatus('🎉 刻晴PMX模型加载成功！');
              console.log('🎉 刻晴PMX模型已添加到场景');
              
              // 加载VMD动画
              loadVMDAnimation(mmd, loader);
            },
            (progress: any) => {
              if (progress.lengthComputable) {
                const percent = Math.round((progress.loaded / progress.total) * 100);
                console.log(`📥 PMX加载进度: ${percent}%`);
                setStatus(`📥 加载刻晴模型: ${percent}%`);
              } else {
                console.log('📥 PMX加载进度:', progress.loaded, 'bytes');
                setStatus(`📥 已加载: ${Math.round(progress.loaded / 1024)}KB`);
              }
            },
            (error: any) => {
              console.error(`❌ 路径 ${currentPath} 加载失败:`, error);
              console.error('错误详情:', {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
              });
              
              // 尝试下一个路径
              tryLoadPMX(pathIndex + 1);
            }
          );
        };
        
        // 开始尝试第一个路径
        tryLoadPMX(0);

      } catch (loaderError) {
        console.error('❌ MMDLoader创建失败:', loaderError);
        setStatus(`❌ MMDLoader创建失败: ${(loaderError as Error).message}`);
        
        // 创建占位符模型
        createKeqingPlaceholder();
      }
    };

    const loadVMDAnimation = (model: any, loader: any) => {
      try {
        console.log('🎬 开始加载VMD动画...');
        setStatus('🎬 开始加载VMD动画...');

        // 尝试加载VMD文件
        const vmdPaths = [
          './assets/models/杂鱼 杂鱼（绝区零）.vmd',
          'assets/models/杂鱼 杂鱼（绝区零）.vmd',
          '/assets/models/杂鱼 杂鱼（绝区零）.vmd'
        ];

        const tryLoadVMD = (pathIndex: number) => {
          if (pathIndex >= vmdPaths.length) {
            console.warn('❌ 所有VMD路径都尝试失败');
            setStatus('⚠️ VMD动画加载失败，但模型正常显示');
            return;
          }

          const vmdPath = vmdPaths[pathIndex];
          console.log(`📁 尝试VMD路径 ${pathIndex + 1}/${vmdPaths.length}: ${vmdPath}`);
          setStatus(`📁 尝试VMD路径 ${pathIndex + 1}/${vmdPaths.length}`);

          loader.loadAnimation(
            vmdPath,
            model,
            (vmd: any) => {
              console.log('🎉 VMD动画加载成功!', vmd);
              console.log('动画详细信息:', {
                duration: vmd.duration || 'Unknown',
                tracks: vmd.tracks?.length || 0,
                name: vmd.name || 'Unknown'
              });

              // 添加动画到助手
              helper.add(model, {
                animation: vmd,
                physics: false
              });

              // 获取混合器
              const helperObject = helper.objects.get(model);
              if (helperObject && helperObject.mixer) {
                const mixer = helperObject.mixer;
                console.log('✅ 动画混合器创建成功');

                // 播放动画
                if (mixer._actions && mixer._actions.length > 0) {
                  const action = mixer._actions[0];
                  action.setLoop(THREE.LoopRepeat, Infinity);
                  action.play();
                  console.log('🎭 开始播放VMD动画');
                  setStatus('🎭 刻晴正在跳舞！点击可以控制动画');
                }
              }
            },
            (progress: any) => {
              console.log('📥 VMD加载进度:', progress);
              setStatus('📥 正在加载VMD动画...');
            },
            (error: any) => {
              console.error(`❌ VMD路径 ${vmdPath} 加载失败:`, error);
              console.error('VMD错误详情:', {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
              });
              
              // 尝试下一个路径
              tryLoadVMD(pathIndex + 1);
            }
          );
        };

        // 开始尝试第一个VMD路径
        tryLoadVMD(0);

      } catch (animationError) {
        console.error('❌ VMD动画系统初始化失败:', animationError);
        setStatus('⚠️ 动画系统初始化失败，但模型正常显示');
      }
    };

    const createKeqingPlaceholder = () => {
      console.log('🎭 创建刻晴风格占位符...');
      setStatus('🎭 使用刻晴风格占位符模型');
      
      const group = new THREE.Group();
      
      // 刻晴的紫色主题色
      const keqingPurple = 0x6B46C1;
      const skinColor = 0xFFDBB5;
      const hairColor = 0x4C1D95;

      // 身体
      const bodyGeometry = new THREE.CapsuleGeometry(2, 8, 8, 16);
      const bodyMaterial = new THREE.MeshLambertMaterial({ color: keqingPurple });
      const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
      body.position.y = 8;
      body.castShadow = true;
      group.add(body);

      // 头部
      const headGeometry = new THREE.SphereGeometry(1.5, 16, 16);
      const headMaterial = new THREE.MeshLambertMaterial({ color: skinColor });
      const head = new THREE.Mesh(headGeometry, headMaterial);
      head.position.y = 14;
      head.castShadow = true;
      group.add(head);

      // 头发
      const hairGeometry = new THREE.SphereGeometry(1.6, 16, 16);
      const hairMaterial = new THREE.MeshLambertMaterial({ 
        color: hairColor, 
        transparent: true, 
        opacity: 0.8 
      });
      const hair = new THREE.Mesh(hairGeometry, hairMaterial);
      hair.position.y = 14.5;
      hair.scale.set(1, 0.8, 1);
      hair.castShadow = true;
      group.add(hair);

      // 双马尾
      const ponytailGeometry = new THREE.SphereGeometry(0.6, 8, 8);
      const ponytailMaterial = new THREE.MeshLambertMaterial({ color: hairColor });
      
      const leftPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
      leftPonytail.position.set(-1.8, 13, -1);
      leftPonytail.scale.set(1, 3, 1);
      leftPonytail.castShadow = true;
      group.add(leftPonytail);
      
      const rightPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
      rightPonytail.position.set(1.8, 13, -1);
      rightPonytail.scale.set(1, 3, 1);
      rightPonytail.castShadow = true;
      group.add(rightPonytail);

      scene.add(group);
      
      // 添加简单动画
      const animatePlaceholder = () => {
        const time = Date.now() * 0.001;
        group.position.y = Math.sin(time * 2) * 0.5;
        group.rotation.y = Math.sin(time * 1.5) * 0.1;
        requestAnimationFrame(animatePlaceholder);
      };
      animatePlaceholder();
      
      console.log('🎭 刻晴风格占位符模型已创建');
    };

    // 延迟初始化，确保DOM已准备好
    setTimeout(init, 100);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      if (renderer && mountRef.current && renderer.domElement) {
        try {
          mountRef.current.removeChild(renderer.domElement);
        } catch (e) {
          console.warn('清理渲染器时出错:', e);
        }
        renderer.dispose();
      }
    };
  }, []);

  return (
    <div>
      <div style={{ 
        position: 'absolute', 
        top: 10, 
        left: 10, 
        color: 'white', 
        zIndex: 100,
        background: 'rgba(0,0,0,0.7)',
        padding: '10px',
        borderRadius: '5px',
        maxWidth: '350px',
        fontSize: '14px'
      }}>
        <strong>静态MMD测试 (无动态导入)</strong>
        <br />
        {status}
        <br />
        <small>按F12打开开发者工具查看详细日志</small>
      </div>
      <div ref={mountRef} style={{ width: '100%', height: '100vh' }} />
    </div>
  );
};

export default StaticMMDTest;