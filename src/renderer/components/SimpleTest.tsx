import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';
import { useAppStore } from '../store/appStore';

const SimpleTest: React.FC = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const { setLoading, setError } = useAppStore();

  useEffect(() => {
    if (!mountRef.current) return;

    console.log('🔧 开始简单的Three.js测试...');
    console.log('Three.js版本:', THREE.REVISION);

    try {
      // 创建基本的Three.js场景
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      const renderer = new THREE.WebGLRenderer();

      renderer.setSize(window.innerWidth, window.innerHeight);
      mountRef.current.appendChild(renderer.domElement);

      // 创建一个简单的立方体
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material = new THREE.MeshBasicMaterial({ color: 0x6B46C1 });
      const cube = new THREE.Mesh(geometry, material);
      scene.add(cube);

      camera.position.z = 5;

      // 动画循环
      const animate = () => {
        requestAnimationFrame(animate);
        cube.rotation.x += 0.01;
        cube.rotation.y += 0.01;
        renderer.render(scene, camera);
      };

      animate();
      console.log('✅ Three.js基础测试成功！');
      
      // 关闭加载状态
      setLoading(false);

      return () => {
        if (mountRef.current && renderer.domElement) {
          mountRef.current.removeChild(renderer.domElement);
        }
        renderer.dispose();
      };
    } catch (error) {
      console.error('❌ Three.js测试失败:', error);
      setError(`Three.js测试失败: ${(error as Error).message}`);
      setLoading(false);
    }
  }, []);

  return (
    <div>
      <div style={{ 
        position: 'absolute', 
        top: 10, 
        left: 10, 
        color: 'white', 
        zIndex: 100,
        background: 'rgba(0,0,0,0.7)',
        padding: '10px',
        borderRadius: '5px'
      }}>
        Three.js 基础测试
      </div>
      <div ref={mountRef} />
    </div>
  );
};

export default SimpleTest;