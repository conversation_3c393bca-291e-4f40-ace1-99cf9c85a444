import React, { useEffect, useState } from 'react';
import SimpleTest from './components/SimpleTest';
import MMDTest from './components/MMDTest';
import BasicMMDTest from './components/BasicMMDTest';
import StaticMMDTest from './components/StaticMMDTest';
import KeqingModel from './components/KeqingModel';
import SimpleAnimationTest from './test/SimpleAnimationTest';
import ContextMenu from './components/ContextMenu';
import { useAppStore } from './store/appStore';
import { useWindowDrag } from './hooks/useWindowDrag';

const App: React.FC = () => {
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const { isLoading, error } = useAppStore();
  const { handleMouseDown } = useWindowDrag();
  
  // 测试模式选择 - 现在使用完整的KeqingModel
  const useSimpleTest = false;    // Three.js基础测试
  const useMMDTest = false;       // MMD加载测试
  const useBasicMMDTest = false;  // 基础MMD测试（动态导入）
  const useStaticMMDTest = false; // 静态MMD测试（静态导入）
  const useSimpleAnimationTest = false; // 简单动画测试
  // 使用完整的KeqingModel组件

  useEffect(() => {
    // 初始化应用
    console.log('刻晴桌面宠物启动中...');
    
    // 监听右键菜单
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      setContextMenuPosition({ x: e.clientX, y: e.clientY });
      setShowContextMenu(true);
    };

    // 监听点击关闭菜单
    const handleClick = () => {
      setShowContextMenu(false);
    };

    // 监听键盘快捷键
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+Q 或 Alt+F4 退出应用
      if ((e.ctrlKey && e.key === 'q') || (e.altKey && e.key === 'F4')) {
        e.preventDefault();
        if (window.electronAPI) {
          window.electronAPI.quitApp();
        }
      }
      // Escape 隐藏窗口
      else if (e.key === 'Escape') {
        e.preventDefault();
        if (window.electronAPI) {
          window.electronAPI.hideWindow();
        }
      }
    };

    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('click', handleClick);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('click', handleClick);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  if (error) {
    return (
      <div style={{ 
        color: 'red', 
        padding: '20px', 
        background: 'rgba(0,0,0,0.8)',
        borderRadius: '8px',
        margin: '20px'
      }}>
        错误: {error}
      </div>
    );
  }

  return (
    <div className="app">
      {/* 拖拽区域 - 使用CSS拖拽 */}
      <div className="drag-area-css" />
      
      {/* 手动拖拽区域 - 用于高级拖拽控制 */}
      <div 
        className="drag-area-manual"
        onMouseDown={handleMouseDown}
      />
      
      {/* 3D刻晴模型 */}
      {useSimpleTest ? <SimpleTest /> : (useMMDTest ? <MMDTest /> : (useBasicMMDTest ? <BasicMMDTest /> : (useStaticMMDTest ? <StaticMMDTest /> : (useSimpleAnimationTest ? <SimpleAnimationTest /> : <KeqingModel />))))}
      
      {/* 加载指示器 */}
      {isLoading && !useMMDTest && !useSimpleTest && !useBasicMMDTest && !useStaticMMDTest && !useSimpleAnimationTest && (
        <div className="loading-indicator">
          <div className="loading-spinner"></div>
          <p>正在加载刻晴...</p>
        </div>
      )}
      
      {/* 退出按钮 */}
      <button 
        className="exit-button"
        onClick={() => window.electronAPI?.quitApp()}
        title="退出应用 (Ctrl+Q)"
      >
        ✕
      </button>

      {/* 右键菜单 */}
      {showContextMenu && (
        <ContextMenu 
          position={contextMenuPosition}
          onClose={() => setShowContextMenu(false)}
        />
      )}
    </div>
  );
};

export default App;