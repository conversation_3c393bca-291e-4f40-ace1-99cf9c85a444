import React from 'react';
import { render, screen } from '@testing-library/react';
import App from '../App';

// Mock KeqingModel组件
jest.mock('../components/KeqingModel', () => {
  return function MockKeqingModel() {
    return <div data-testid="keqing-model">Keqing Model</div>;
  };
});

describe('App Component', () => {
  test('renders without crashing', () => {
    render(<App />);
    expect(screen.getByTestId('keqing-model')).toBeInTheDocument();
  });

  test('shows loading indicator when loading', () => {
    // 这里需要mock useAppStore来返回loading状态
    render(<App />);
    // 由于初始状态isLoading为true，应该显示加载指示器
    expect(screen.getByText('正在加载刻晴...')).toBeInTheDocument();
  });
});