import * as THREE from 'three';

/**
 * VMD关键帧数据接口
 */
export interface VMDKeyframe {
  frameNumber: number;
  position?: THREE.Vector3;
  rotation?: THREE.Quaternion;
  interpolation?: number[];
}

/**
 * VMD骨骼动画数据
 */
export interface VMDBoneAnimation {
  boneName: string;
  keyframes: VMDKeyframe[];
}

/**
 * VMD表情动画数据
 */
export interface VMDMorphAnimation {
  morphName: string;
  keyframes: Array<{
    frameNumber: number;
    weight: number;
  }>;
}

/**
 * VMD相机动画数据
 */
export interface VMDCameraAnimation {
  keyframes: Array<{
    frameNumber: number;
    position: THREE.Vector3;
    rotation: THREE.Vector3;
    distance: number;
    fov: number;
    interpolation: number[];
  }>;
}

/**
 * VMD动画数据
 */
export interface VMDAnimationData {
  bones: VMDBoneAnimation[];
  morphs: VMDMorphAnimation[];
  camera?: VMDCameraAnimation;
  frameRate: number;
  duration: number;
}

/**
 * VMD加载选项
 */
export interface VMDLoadOptions {
  frameRate?: number;
  scale?: number;
  enableBoneAnimation?: boolean;
  enableMorphAnimation?: boolean;
  enableCameraAnimation?: boolean;
}

/**
 * VMD动画文件加载器
 * 负责解析VMD格式的动画文件并转换为Three.js动画剪辑
 */
export class VMDLoader {
  private frameRate: number = 30; // 默认帧率
  private textDecoder: TextDecoder = new TextDecoder('shift-jis');

  constructor() {
    console.log('VMDLoader初始化完成');
  }

  /**
   * 加载VMD动画文件
   */
  public async loadVMD(
    url: string, 
    options: VMDLoadOptions = {}
  ): Promise<VMDAnimationData> {
    try {
      console.log('🎬 开始加载VMD文件:', url);
      
      // 尝试加载真实的VMD文件
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`VMD文件不存在: ${url} (状态: ${response.status})`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      console.log('📁 VMD文件读取成功，大小:', arrayBuffer.byteLength, 'bytes');
      
      // 解析VMD文件
      const animationData = await this.parseVMDFile(arrayBuffer, options);
      
      console.log('✅ VMD文件解析完成:', url);
      return animationData;
    } catch (error) {
      console.warn('⚠️ VMD文件加载失败，使用模拟数据:', error);
      // 如果真实VMD加载失败，使用模拟数据
      return await this.createMockVMDData(options);
    }
  }

  /**
   * 解析VMD文件
   */
  private async parseVMDFile(
    buffer: ArrayBuffer,
    options: VMDLoadOptions
  ): Promise<VMDAnimationData> {
    console.log('🔍 开始解析VMD文件...');
    const view = new DataView(buffer);
    let offset = 0;

    try {
      // 读取文件头 (30字节)
      const header = new Uint8Array(buffer, offset, 30);
      const headerText = new TextDecoder('shift-jis').decode(header).replace(/\0/g, '');
      offset += 30;
      console.log('📋 VMD文件头:', headerText);

      // 读取模型名 (20字节)
      const modelNameBytes = new Uint8Array(buffer, offset, 20);
      const modelName = new TextDecoder('shift-jis').decode(modelNameBytes).replace(/\0/g, '');
      offset += 20;
      console.log('🎭 目标模型:', modelName);

      // 读取骨骼关键帧数量
      const boneKeyframeCount = view.getUint32(offset, true);
      offset += 4;
      console.log('🦴 骨骼关键帧数量:', boneKeyframeCount);

      // 解析骨骼关键帧
      const bones: VMDBoneAnimation[] = [];
      for (let i = 0; i < boneKeyframeCount; i++) {
        // 骨骼名 (15字节)
        const boneNameBytes = new Uint8Array(buffer, offset, 15);
        const boneName = new TextDecoder('shift-jis').decode(boneNameBytes).replace(/\0/g, '');
        offset += 15;

        // 帧号
        const frameNumber = view.getUint32(offset, true);
        offset += 4;

        // 位置 (x, y, z)
        const x = view.getFloat32(offset, true);
        const y = view.getFloat32(offset + 4, true);
        const z = view.getFloat32(offset + 8, true);
        offset += 12;

        // 旋转四元数 (x, y, z, w)
        const qx = view.getFloat32(offset, true);
        const qy = view.getFloat32(offset + 4, true);
        const qz = view.getFloat32(offset + 8, true);
        const qw = view.getFloat32(offset + 12, true);
        offset += 16;

        // 插值参数 (64字节)
        const interpolation: number[] = [];
        for (let j = 0; j < 64; j++) {
          interpolation.push(view.getUint8(offset + j));
        }
        offset += 64;

        // 查找或创建骨骼动画
        let boneAnim = bones.find(b => b.boneName === boneName);
        if (!boneAnim) {
          boneAnim = { boneName, keyframes: [] };
          bones.push(boneAnim);
        }

        boneAnim.keyframes.push({
          frameNumber,
          position: new THREE.Vector3(x, y, z),
          rotation: new THREE.Quaternion(qx, qy, qz, qw),
          interpolation
        });
      }

      console.log('🦴 解析了', bones.length, '个骨骼的动画数据');

      // 读取表情关键帧数量
      let morphKeyframeCount = 0;
      const morphs: VMDMorphAnimation[] = [];
      
      if (offset < buffer.byteLength - 4) {
        morphKeyframeCount = view.getUint32(offset, true);
        offset += 4;
        console.log('😊 表情关键帧数量:', morphKeyframeCount);

        // 解析表情关键帧
        for (let i = 0; i < morphKeyframeCount && offset < buffer.byteLength - 23; i++) {
          // 表情名 (15字节)
          const morphNameBytes = new Uint8Array(buffer, offset, 15);
          const morphName = new TextDecoder('shift-jis').decode(morphNameBytes).replace(/\0/g, '');
          offset += 15;

          // 帧号
          const frameNumber = view.getUint32(offset, true);
          offset += 4;

          // 权重
          const weight = view.getFloat32(offset, true);
          offset += 4;

          // 查找或创建表情动画
          let morphAnim = morphs.find(m => m.morphName === morphName);
          if (!morphAnim) {
            morphAnim = { morphName, keyframes: [] };
            morphs.push(morphAnim);
          }

          morphAnim.keyframes.push({ frameNumber, weight });
        }

        console.log('😊 解析了', morphs.length, '个表情的动画数据');
      }

      // 计算动画持续时间
      let maxFrame = 0;
      bones.forEach(bone => {
        bone.keyframes.forEach(kf => {
          maxFrame = Math.max(maxFrame, kf.frameNumber);
        });
      });
      morphs.forEach(morph => {
        morph.keyframes.forEach(kf => {
          maxFrame = Math.max(maxFrame, kf.frameNumber);
        });
      });

      const frameRate = options.frameRate || this.frameRate;
      const duration = maxFrame / frameRate;

      console.log('⏱️ 动画信息:', {
        maxFrame,
        frameRate,
        duration: duration.toFixed(2) + 's'
      });

      return {
        bones,
        morphs,
        frameRate,
        duration
      };
    } catch (parseError) {
      console.error('❌ VMD解析错误:', parseError);
      throw parseError;
    }
  }

  /**
   * 创建模拟的VMD动画数据（临时实现）
   */
  private async createMockVMDData(options: VMDLoadOptions): Promise<VMDAnimationData> {
    const frameRate = options.frameRate || this.frameRate;
    const duration = 3.0; // 3秒动画

    // 创建骨骼动画数据
    const bones: VMDBoneAnimation[] = [];
    
    // 模拟头部动画
    bones.push({
      boneName: 'head',
      keyframes: [
        {
          frameNumber: 0,
          position: new THREE.Vector3(0, 0, 0),
          rotation: new THREE.Quaternion(0, 0, 0, 1)
        },
        {
          frameNumber: frameRate,
          position: new THREE.Vector3(0, 0.1, 0),
          rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(0.1, 0, 0))
        },
        {
          frameNumber: frameRate * 2,
          position: new THREE.Vector3(0, 0, 0),
          rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(-0.1, 0, 0))
        },
        {
          frameNumber: frameRate * 3,
          position: new THREE.Vector3(0, 0, 0),
          rotation: new THREE.Quaternion(0, 0, 0, 1)
        }
      ]
    });

    // 模拟身体动画
    bones.push({
      boneName: 'body',
      keyframes: [
        {
          frameNumber: 0,
          position: new THREE.Vector3(0, 0, 0),
          rotation: new THREE.Quaternion(0, 0, 0, 1)
        },
        {
          frameNumber: frameRate * 1.5,
          position: new THREE.Vector3(0, 0.05, 0),
          rotation: new THREE.Quaternion().setFromEuler(new THREE.Euler(0, 0.05, 0))
        },
        {
          frameNumber: frameRate * 3,
          position: new THREE.Vector3(0, 0, 0),
          rotation: new THREE.Quaternion(0, 0, 0, 1)
        }
      ]
    });

    // 创建表情动画数据
    const morphs: VMDMorphAnimation[] = [];
    
    // 模拟眨眼动画
    morphs.push({
      morphName: 'blink',
      keyframes: [
        { frameNumber: 0, weight: 0 },
        { frameNumber: frameRate * 0.5, weight: 1 },
        { frameNumber: frameRate, weight: 0 },
        { frameNumber: frameRate * 2.5, weight: 1 },
        { frameNumber: frameRate * 3, weight: 0 }
      ]
    });

    // 模拟微笑动画
    morphs.push({
      morphName: 'smile',
      keyframes: [
        { frameNumber: 0, weight: 0 },
        { frameNumber: frameRate, weight: 0.5 },
        { frameNumber: frameRate * 2, weight: 0.8 },
        { frameNumber: frameRate * 3, weight: 0 }
      ]
    });

    return {
      bones,
      morphs,
      frameRate,
      duration
    };
  }

  /**
   * 将VMD动画数据转换为Three.js动画剪辑
   */
  public convertToAnimationClip(
    vmdData: VMDAnimationData, 
    clipName: string = 'vmd_animation'
  ): THREE.AnimationClip {
    const tracks: THREE.KeyframeTrack[] = [];

    // 转换骨骼动画
    vmdData.bones.forEach(boneAnim => {
      const positionTimes: number[] = [];
      const positionValues: number[] = [];
      const rotationTimes: number[] = [];
      const rotationValues: number[] = [];

      boneAnim.keyframes.forEach(keyframe => {
        const time = keyframe.frameNumber / vmdData.frameRate;

        if (keyframe.position) {
          positionTimes.push(time);
          positionValues.push(
            keyframe.position.x,
            keyframe.position.y,
            keyframe.position.z
          );
        }

        if (keyframe.rotation) {
          rotationTimes.push(time);
          rotationValues.push(
            keyframe.rotation.x,
            keyframe.rotation.y,
            keyframe.rotation.z,
            keyframe.rotation.w
          );
        }
      });

      // 创建位置轨道
      if (positionTimes.length > 0) {
        const positionTrack = new THREE.VectorKeyframeTrack(
          `${boneAnim.boneName}.position`,
          positionTimes,
          positionValues
        );
        tracks.push(positionTrack);
      }

      // 创建旋转轨道
      if (rotationTimes.length > 0) {
        const rotationTrack = new THREE.QuaternionKeyframeTrack(
          `${boneAnim.boneName}.quaternion`,
          rotationTimes,
          rotationValues
        );
        tracks.push(rotationTrack);
      }
    });

    // 转换表情动画
    vmdData.morphs.forEach(morphAnim => {
      const times: number[] = [];
      const values: number[] = [];

      morphAnim.keyframes.forEach(keyframe => {
        const time = keyframe.frameNumber / vmdData.frameRate;
        times.push(time);
        values.push(keyframe.weight);
      });

      if (times.length > 0) {
        const morphTrack = new THREE.NumberKeyframeTrack(
          `morphTargetInfluences[${morphAnim.morphName}]`,
          times,
          values
        );
        tracks.push(morphTrack);
      }
    });

    return new THREE.AnimationClip(clipName, vmdData.duration, tracks);
  }

  /**
   * 批量加载VMD文件
   */
  public async loadMultipleVMD(
    urls: string[], 
    options: VMDLoadOptions = {}
  ): Promise<Map<string, VMDAnimationData>> {
    const results = new Map<string, VMDAnimationData>();
    
    const promises = urls.map(async (url) => {
      try {
        const data = await this.loadVMD(url, options);
        results.set(url, data);
      } catch (error) {
        console.error(`VMD文件加载失败: ${url}`, error);
      }
    });

    await Promise.allSettled(promises);
    
    console.log(`批量加载VMD完成: ${results.size}/${urls.length}`);
    return results;
  }

  /**
   * 创建动画混合器动作
   */
  public createAnimationAction(
    mixer: THREE.AnimationMixer,
    vmdData: VMDAnimationData,
    clipName: string = 'vmd_animation'
  ): THREE.AnimationAction {
    const clip = this.convertToAnimationClip(vmdData, clipName);
    const action = mixer.clipAction(clip);
    
    // 设置默认属性
    action.setLoop(THREE.LoopOnce, 1);
    action.clampWhenFinished = true;
    
    return action;
  }

  /**
   * 解析VMD文件头部信息
   */
  private parseVMDHeader(buffer: ArrayBuffer): {
    signature: string;
    modelName: string;
  } {
    const view = new DataView(buffer);
    
    // VMD文件签名 (30字节)
    const signatureBytes = new Uint8Array(buffer, 0, 30);
    const signature = this.textDecoder.decode(signatureBytes).replace(/\0/g, '');
    
    // 模型名称 (20字节)
    const modelNameBytes = new Uint8Array(buffer, 30, 20);
    const modelName = this.textDecoder.decode(modelNameBytes).replace(/\0/g, '');
    
    return { signature, modelName };
  }

  /**
   * 解析骨骼动画数据
   */
  private parseBoneKeyframes(
    buffer: ArrayBuffer, 
    offset: number, 
    count: number
  ): { bones: VMDBoneAnimation[]; nextOffset: number } {
    const view = new DataView(buffer);
    const bones: VMDBoneAnimation[] = [];
    let currentOffset = offset;

    // 这里应该实现实际的VMD骨骼数据解析
    // 由于VMD格式复杂，这里提供一个简化的实现框架
    
    for (let i = 0; i < count; i++) {
      // 骨骼名称 (15字节)
      const boneNameBytes = new Uint8Array(buffer, currentOffset, 15);
      const boneName = this.textDecoder.decode(boneNameBytes).replace(/\0/g, '');
      currentOffset += 15;

      // 帧号 (4字节)
      const frameNumber = view.getUint32(currentOffset, true);
      currentOffset += 4;

      // 位置 (12字节 - 3个float)
      const x = view.getFloat32(currentOffset, true);
      const y = view.getFloat32(currentOffset + 4, true);
      const z = view.getFloat32(currentOffset + 8, true);
      currentOffset += 12;

      // 旋转 (16字节 - 4个float)
      const qx = view.getFloat32(currentOffset, true);
      const qy = view.getFloat32(currentOffset + 4, true);
      const qz = view.getFloat32(currentOffset + 8, true);
      const qw = view.getFloat32(currentOffset + 12, true);
      currentOffset += 16;

      // 插值参数 (64字节)
      const interpolation: number[] = [];
      for (let j = 0; j < 64; j++) {
        interpolation.push(view.getUint8(currentOffset + j));
      }
      currentOffset += 64;

      // 查找或创建骨骼动画
      let boneAnim = bones.find(b => b.boneName === boneName);
      if (!boneAnim) {
        boneAnim = { boneName, keyframes: [] };
        bones.push(boneAnim);
      }

      // 添加关键帧
      boneAnim.keyframes.push({
        frameNumber,
        position: new THREE.Vector3(x, y, z),
        rotation: new THREE.Quaternion(qx, qy, qz, qw),
        interpolation
      });
    }

    return { bones, nextOffset: currentOffset };
  }

  /**
   * 解析表情动画数据
   */
  private parseMorphKeyframes(
    buffer: ArrayBuffer, 
    offset: number, 
    count: number
  ): { morphs: VMDMorphAnimation[]; nextOffset: number } {
    const view = new DataView(buffer);
    const morphs: VMDMorphAnimation[] = [];
    let currentOffset = offset;

    for (let i = 0; i < count; i++) {
      // 表情名称 (15字节)
      const morphNameBytes = new Uint8Array(buffer, currentOffset, 15);
      const morphName = this.textDecoder.decode(morphNameBytes).replace(/\0/g, '');
      currentOffset += 15;

      // 帧号 (4字节)
      const frameNumber = view.getUint32(currentOffset, true);
      currentOffset += 4;

      // 权重 (4字节)
      const weight = view.getFloat32(currentOffset, true);
      currentOffset += 4;

      // 查找或创建表情动画
      let morphAnim = morphs.find(m => m.morphName === morphName);
      if (!morphAnim) {
        morphAnim = { morphName, keyframes: [] };
        morphs.push(morphAnim);
      }

      // 添加关键帧
      morphAnim.keyframes.push({ frameNumber, weight });
    }

    return { morphs, nextOffset: currentOffset };
  }

  /**
   * 设置帧率
   */
  public setFrameRate(frameRate: number): void {
    this.frameRate = frameRate;
  }

  /**
   * 获取帧率
   */
  public getFrameRate(): number {
    return this.frameRate;
  }

  /**
   * 验证VMD文件格式
   */
  public validateVMDFile(buffer: ArrayBuffer): boolean {
    try {
      const header = this.parseVMDHeader(buffer);
      return header.signature.includes('Vocaloid Motion Data');
    } catch (error) {
      console.error('VMD文件验证失败:', error);
      return false;
    }
  }

  /**
   * 获取VMD文件信息
   */
  public getVMDInfo(buffer: ArrayBuffer): {
    signature: string;
    modelName: string;
    boneKeyframeCount: number;
    morphKeyframeCount: number;
  } | null {
    try {
      const view = new DataView(buffer);
      const header = this.parseVMDHeader(buffer);
      
      // 骨骼关键帧数量 (4字节，偏移50)
      const boneKeyframeCount = view.getUint32(50, true);
      
      // 计算表情关键帧偏移
      const morphOffset = 54 + boneKeyframeCount * 111; // 每个骨骼关键帧111字节
      const morphKeyframeCount = view.getUint32(morphOffset, true);
      
      return {
        signature: header.signature,
        modelName: header.modelName,
        boneKeyframeCount,
        morphKeyframeCount
      };
    } catch (error) {
      console.error('获取VMD文件信息失败:', error);
      return null;
    }
  }

  /**
   * 销毁加载器
   */
  public dispose(): void {
    console.log('VMDLoader已销毁');
  }
}