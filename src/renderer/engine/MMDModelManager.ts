import * as THREE from 'three';
import { M<PERSON>Loader, MMDModelData, MMDLoadOptions } from './MMDLoader';

export interface ModelLoadResult {
  success: boolean;
  model?: MMDModelData;
  error?: string;
}

export interface ModelManagerConfig {
  maxCacheSize?: number;
  enablePreloading?: boolean;
  defaultModelPath?: string;
  retryAttempts?: number;
  retryDelay?: number;
}

export class MMDModelManager {
  private loader: MMDLoader;
  private config: Required<ModelManagerConfig>;
  private loadingPromises: Map<string, Promise<MMDModelData>> = new Map();
  private loadAttempts: Map<string, number> = new Map();
  private defaultModel: MMDModelData | null = null;

  // 事件回调
  private onLoadStart?: (url: string) => void;
  private onLoadProgress?: (url: string, progress: number) => void;
  private onLoadComplete?: (url: string, model: MMDModelData) => void;
  private onLoadError?: (url: string, error: string) => void;

  constructor(config: ModelManagerConfig = {}) {
    this.config = {
      maxCacheSize: 5,
      enablePreloading: true,
      defaultModelPath: '',
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    };

    this.loader = new MMDLoader();
    this.initializeDefaultModel();
  }

  /**
   * 初始化默认模型
   */
  private async initializeDefaultModel(): Promise<void> {
    try {
      this.defaultModel = await this.loader.loadPMX('default', {
        scale: 1.0,
        position: { x: 0, y: -1, z: 0 }
      });
      console.log('默认模型初始化完成');
    } catch (error) {
      console.error('默认模型初始化失败:', error);
    }
  }

  /**
   * 加载MMD模型
   */
  public async loadModel(
    url: string, 
    options: MMDLoadOptions = {}
  ): Promise<ModelLoadResult> {
    // 检查是否已经在加载中
    if (this.loadingPromises.has(url)) {
      try {
        const model = await this.loadingPromises.get(url)!;
        return { success: true, model };
      } catch (error) {
        return { 
          success: false, 
          error: `模型加载失败: ${(error as Error).message}` 
        };
      }
    }

    // 开始加载
    const loadPromise = this.performLoad(url, options);
    this.loadingPromises.set(url, loadPromise);

    try {
      if (this.onLoadStart) {
        this.onLoadStart(url);
      }

      const model = await loadPromise;
      
      if (this.onLoadComplete) {
        this.onLoadComplete(url, model);
      }

      // 清理加载状态
      this.loadingPromises.delete(url);
      this.loadAttempts.delete(url);

      return { success: true, model };
    } catch (error) {
      const errorMessage = `模型加载失败: ${(error as Error).message}`;
      
      if (this.onLoadError) {
        this.onLoadError(url, errorMessage);
      }

      // 清理加载状态
      this.loadingPromises.delete(url);

      // 检查是否需要重试
      const attempts = this.loadAttempts.get(url) || 0;
      if (attempts < this.config.retryAttempts) {
        console.log(`模型加载失败，准备重试 (${attempts + 1}/${this.config.retryAttempts}):`, url);
        this.loadAttempts.set(url, attempts + 1);
        
        // 延迟后重试
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        return this.loadModel(url, options);
      }

      this.loadAttempts.delete(url);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 执行实际的加载操作
   */
  private async performLoad(
    url: string, 
    options: MMDLoadOptions
  ): Promise<MMDModelData> {
    try {
      // 检查缓存大小
      this.checkCacheSize();
      
      // 加载模型
      const model = await this.loader.loadPMX(url, options);
      
      return model;
    } catch (error) {
      console.error('模型加载执行失败:', error);
      throw error;
    }
  }

  /**
   * 检查缓存大小并清理
   */
  private checkCacheSize(): void {
    const cacheInfo = this.loader.getCacheInfo();
    if (cacheInfo.count >= this.config.maxCacheSize) {
      console.log('缓存已满，清理旧模型');
      // 这里可以实现LRU策略，暂时简单清理所有缓存
      this.loader.clearCache();
    }
  }

  /**
   * 预加载模型
   */
  public async preloadModel(url: string, options?: MMDLoadOptions): Promise<boolean> {
    if (!this.config.enablePreloading) {
      console.log('预加载已禁用');
      return false;
    }

    try {
      await this.loader.preloadModel(url, options);
      return true;
    } catch (error) {
      console.error('模型预加载失败:', error);
      return false;
    }
  }

  /**
   * 获取默认模型
   */
  public getDefaultModel(): MMDModelData | null {
    return this.defaultModel;
  }

  /**
   * 批量预加载模型
   */
  public async preloadModels(
    models: Array<{ url: string; options?: MMDLoadOptions }>
  ): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    const promises = models.map(async ({ url, options }) => {
      try {
        await this.preloadModel(url, options);
        success++;
      } catch (error) {
        console.error(`预加载失败: ${url}`, error);
        failed++;
      }
    });

    await Promise.allSettled(promises);
    
    console.log(`批量预加载完成: 成功 ${success}, 失败 ${failed}`);
    return { success, failed };
  }

  /**
   * 设置加载事件回调
   */
  public setLoadCallbacks(callbacks: {
    onLoadStart?: (url: string) => void;
    onLoadProgress?: (url: string, progress: number) => void;
    onLoadComplete?: (url: string, model: MMDModelData) => void;
    onLoadError?: (url: string, error: string) => void;
  }): void {
    this.onLoadStart = callbacks.onLoadStart;
    this.onLoadProgress = callbacks.onLoadProgress;
    this.onLoadComplete = callbacks.onLoadComplete;
    this.onLoadError = callbacks.onLoadError;
  }

  /**
   * 获取加载状态
   */
  public getLoadingStatus(): {
    isLoading: boolean;
    loadingCount: number;
    loadingUrls: string[];
  } {
    return {
      isLoading: this.loadingPromises.size > 0,
      loadingCount: this.loadingPromises.size,
      loadingUrls: Array.from(this.loadingPromises.keys())
    };
  }

  /**
   * 取消加载
   */
  public cancelLoad(url: string): boolean {
    if (this.loadingPromises.has(url)) {
      this.loadingPromises.delete(url);
      this.loadAttempts.delete(url);
      console.log('已取消模型加载:', url);
      return true;
    }
    return false;
  }

  /**
   * 取消所有加载
   */
  public cancelAllLoads(): void {
    const loadingUrls = Array.from(this.loadingPromises.keys());
    this.loadingPromises.clear();
    this.loadAttempts.clear();
    console.log('已取消所有模型加载:', loadingUrls);
  }

  /**
   * 获取缓存信息
   */
  public getCacheInfo(): { count: number; urls: string[] } {
    return this.loader.getCacheInfo();
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.loader.clearCache();
  }

  /**
   * 销毁管理器
   */
  public dispose(): void {
    this.cancelAllLoads();
    this.loader.dispose();
    this.defaultModel = null;
    console.log('MMDModelManager已销毁');
  }
}