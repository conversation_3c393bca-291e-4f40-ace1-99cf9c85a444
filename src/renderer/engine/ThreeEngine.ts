import * as THREE from 'three';

export interface EngineConfig {
  antialias?: boolean;
  alpha?: boolean;
  powerPreference?: 'default' | 'high-performance' | 'low-power';
  preserveDrawingBuffer?: boolean;
  logarithmicDepthBuffer?: boolean;
}

export interface CameraConfig {
  fov?: number;
  near?: number;
  far?: number;
  position?: { x: number; y: number; z: number };
}

export interface LightConfig {
  ambient?: {
    color?: number;
    intensity?: number;
  };
  directional?: {
    color?: number;
    intensity?: number;
    position?: { x: number; y: number; z: number };
  };
}

export interface RenderStats {
  fps: number;
  frameTime: number;
  triangles: number;
  drawCalls: number;
  memoryUsage: number;
}

export class ThreeEngine {
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private renderer!: THREE.WebGLRenderer;
  private animationId: number | null = null;
  private container: HTMLElement | null = null;

  // 性能监控
  private clock: THREE.Clock;
  private frameCount = 0;
  private lastTime = 0;
  private fps = 0;
  private frameTime = 0;

  // 动画系统集成
  private updateCallbacks: Array<(deltaTime: number) => void> = [];

  // 事件回调
  private onRenderCallback?: (stats: RenderStats) => void;
  private onErrorCallback?: (error: Error) => void;
  private onResizeCallback?: (width: number, height: number) => void;

  constructor(
    container: HTMLElement,
    engineConfig: EngineConfig = {},
    cameraConfig: CameraConfig = {},
    lightConfig: LightConfig = {}
  ) {
    this.container = container;
    this.clock = new THREE.Clock();
    
    try {
      this.initializeRenderer(engineConfig);
      this.initializeScene();
      this.initializeCamera(cameraConfig);
      this.initializeLights(lightConfig);
      this.setupEventListeners();
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 检查WebGL支持
   */
  public static checkWebGLSupport(): { supported: boolean; error?: string } {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (!gl) {
        return {
          supported: false,
          error: 'WebGL不受支持'
        };
      }

      // 检查WebGL扩展
      const webglContext = gl as WebGLRenderingContext;
      const extensions = {
        vertexArrayObject: webglContext.getExtension('OES_vertex_array_object'),
        instancedArrays: webglContext.getExtension('ANGLE_instanced_arrays'),
        elementIndexUint: webglContext.getExtension('OES_element_index_uint')
      };

      return {
        supported: true,
        error: undefined
      };
    } catch (error) {
      return {
        supported: false,
        error: `WebGL检查失败: ${(error as Error).message}`
      };
    }
  }

  /**
   * 初始化渲染器
   */
  private initializeRenderer(config: EngineConfig): void {
    const defaultConfig: Required<EngineConfig> = {
      antialias: true,
      alpha: true,
      powerPreference: 'high-performance',
      preserveDrawingBuffer: false,
      logarithmicDepthBuffer: false
    };

    const finalConfig = { ...defaultConfig, ...config };

    this.renderer = new THREE.WebGLRenderer(finalConfig);
    this.renderer.setSize(this.container!.clientWidth, this.container!.clientHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // 限制像素比以提高性能
    this.renderer.setClearColor(0x000000, 0); // 透明背景
    
    // 启用阴影
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    
    // 色彩管理
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.0;

    this.container!.appendChild(this.renderer.domElement);
  }

  /**
   * 初始化场景
   */
  private initializeScene(): void {
    this.scene = new THREE.Scene();
    
    // 添加雾效果（可选）
    // this.scene.fog = new THREE.Fog(0x000000, 10, 50);
  }

  /**
   * 初始化相机
   */
  private initializeCamera(config: CameraConfig): void {
    const defaultConfig: Required<CameraConfig> = {
      fov: 75,
      near: 0.1,
      far: 1000,
      position: { x: 0, y: 0, z: 5 }
    };

    const finalConfig = { ...defaultConfig, ...config };
    const aspect = this.container!.clientWidth / this.container!.clientHeight;

    this.camera = new THREE.PerspectiveCamera(
      finalConfig.fov,
      aspect,
      finalConfig.near,
      finalConfig.far
    );

    this.camera.position.set(
      finalConfig.position.x,
      finalConfig.position.y,
      finalConfig.position.z
    );
  }

  /**
   * 初始化光照
   */
  private initializeLights(config: LightConfig): void {
    // 环境光
    const ambientConfig = {
      color: 0xffffff,
      intensity: 0.6,
      ...config.ambient
    };
    const ambientLight = new THREE.AmbientLight(ambientConfig.color, ambientConfig.intensity);
    this.scene.add(ambientLight);

    // 方向光
    const directionalConfig = {
      color: 0xffffff,
      intensity: 0.8,
      position: { x: 1, y: 1, z: 1 },
      ...config.directional
    };
    const directionalLight = new THREE.DirectionalLight(
      directionalConfig.color,
      directionalConfig.intensity
    );
    directionalLight.position.set(
      directionalConfig.position.x,
      directionalConfig.position.y,
      directionalConfig.position.z
    );
    
    // 启用阴影
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    
    this.scene.add(directionalLight);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
  }

  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    if (!this.container) return;

    const width = this.container.clientWidth;
    const height = this.container.clientHeight;

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);

    if (this.onResizeCallback) {
      this.onResizeCallback(width, height);
    }
  }

  /**
   * 处理页面可见性变化
   */
  private handleVisibilityChange(): void {
    if (document.hidden) {
      this.pauseRendering();
    } else {
      this.resumeRendering();
    }
  }

  /**
   * 开始渲染循环
   */
  public startRendering(): void {
    if (this.animationId !== null) return;

    this.clock.start();
    this.animate();
  }

  /**
   * 暂停渲染
   */
  public pauseRendering(): void {
    if (this.animationId !== null) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * 恢复渲染
   */
  public resumeRendering(): void {
    if (this.animationId === null) {
      this.startRendering();
    }
  }

  /**
   * 渲染循环
   */
  private animate(): void {
    this.animationId = requestAnimationFrame(this.animate.bind(this));

    const deltaTime = this.clock.getDelta();
    this.updatePerformanceStats(deltaTime);

    // 调用所有更新回调（包括动画系统）
    this.updateCallbacks.forEach(callback => {
      try {
        callback(deltaTime);
      } catch (error) {
        console.error('更新回调执行失败:', error);
      }
    });

    // 渲染场景
    this.renderer.render(this.scene, this.camera);

    // 调用渲染回调
    if (this.onRenderCallback) {
      this.onRenderCallback(this.getRenderStats());
    }
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(deltaTime: number): void {
    this.frameCount++;
    this.frameTime = deltaTime * 1000; // 转换为毫秒

    const currentTime = performance.now();
    if (currentTime - this.lastTime >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
      this.frameCount = 0;
      this.lastTime = currentTime;
    }
  }

  /**
   * 获取渲染统计信息
   */
  public getRenderStats(): RenderStats {
    const info = this.renderer.info;
    return {
      fps: this.fps,
      frameTime: this.frameTime,
      triangles: info.render.triangles,
      drawCalls: info.render.calls,
      memoryUsage: info.memory.geometries + info.memory.textures
    };
  }

  /**
   * 添加对象到场景
   */
  public addToScene(object: THREE.Object3D): void {
    this.scene.add(object);
  }

  /**
   * 从场景移除对象
   */
  public removeFromScene(object: THREE.Object3D): void {
    this.scene.remove(object);
  }

  /**
   * 注册更新回调
   */
  public addUpdateCallback(callback: (deltaTime: number) => void): void {
    this.updateCallbacks.push(callback);
  }

  /**
   * 移除更新回调
   */
  public removeUpdateCallback(callback: (deltaTime: number) => void): void {
    const index = this.updateCallbacks.indexOf(callback);
    if (index > -1) {
      this.updateCallbacks.splice(index, 1);
    }
  }

  /**
   * 获取场景引用
   */
  public getScene(): THREE.Scene {
    return this.scene;
  }

  /**
   * 获取相机引用
   */
  public getCamera(): THREE.PerspectiveCamera {
    return this.camera;
  }

  /**
   * 获取渲染器引用
   */
  public getRenderer(): THREE.WebGLRenderer {
    return this.renderer;
  }

  /**
   * 设置渲染回调
   */
  public setOnRender(callback: (stats: RenderStats) => void): void {
    this.onRenderCallback = callback;
  }

  /**
   * 设置错误回调
   */
  public setOnError(callback: (error: Error) => void): void {
    this.onErrorCallback = callback;
  }

  /**
   * 设置大小变化回调
   */
  public setOnResize(callback: (width: number, height: number) => void): void {
    this.onResizeCallback = callback;
  }

  /**
   * 处理错误
   */
  private handleError(error: Error): void {
    console.error('ThreeEngine Error:', error);
    if (this.onErrorCallback) {
      this.onErrorCallback(error);
    }
  }

  /**
   * 销毁引擎
   */
  public dispose(): void {
    this.pauseRendering();
    
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize.bind(this));
    document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    
    // 清理Three.js资源
    this.scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        object.geometry.dispose();
        if (Array.isArray(object.material)) {
          object.material.forEach(material => material.dispose());
        } else {
          object.material.dispose();
        }
      }
    });

    // 清理渲染器
    this.renderer.dispose();
    
    // 从DOM移除
    if (this.container && this.renderer.domElement.parentNode) {
      this.container.removeChild(this.renderer.domElement);
    }
  }
}