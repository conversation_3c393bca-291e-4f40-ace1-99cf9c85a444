import * as THREE from 'three';
// @ts-ignore - MMDLoader没有类型声明
import { MMDLoader as ThreeMMDLoader } from 'three/examples/jsm/loaders/MMDLoader.js';

export interface MMDModelData {
  model: THREE.Group;
  materials: THREE.Material[];
  textures: THREE.Texture[];
  bones: THREE.Bone[];
  morphs: any[];
}

export interface MMDLoadOptions {
  enablePhysics?: boolean;
  enableAnimation?: boolean;
  scale?: number;
  position?: { x: number; y: number; z: number };
  rotation?: { x: number; y: number; z: number };
}

export class MMDLoader {
  private loader: THREE.FileLoader;
  private textureLoader: THREE.TextureLoader;
  private mmdLoader: ThreeMMDLoader;
  private loadingManager: THREE.LoadingManager;
  private modelCache: Map<string, MMDModelData> = new Map();

  constructor() {
    this.loadingManager = new THREE.LoadingManager();
    this.loader = new THREE.FileLoader(this.loadingManager);
    this.textureLoader = new THREE.TextureLoader(this.loadingManager);
    this.mmdLoader = new ThreeMMDLoader(this.loadingManager);
    
    // 设置加载管理器回调
    this.loadingManager.onLoad = () => {
      console.log('MMD模型加载完成');
    };
    
    this.loadingManager.onProgress = (url, loaded, total) => {
      console.log(`MMD加载进度: ${url} - ${loaded}/${total}`);
    };
    
    this.loadingManager.onError = (url) => {
      console.error('MMD加载错误:', url);
    };
  }

  /**
   * 加载PMX模型文件
   */
  public async loadPMX(
    url: string, 
    options: MMDLoadOptions = {}
  ): Promise<MMDModelData> {
    // 检查缓存
    if (this.modelCache.has(url)) {
      console.log('从缓存加载MMD模型:', url);
      return this.modelCache.get(url)!;
    }

    try {
      console.log('开始加载PMX模型:', url);
      
      // 尝试加载真正的PMX文件
      try {
        const modelData = await this.loadRealPMX(url, options);
        
        // 缓存模型
        this.modelCache.set(url, modelData);
        
        console.log('PMX模型加载成功:', url);
        return modelData;
      } catch (pmxError) {
        console.warn('PMX文件加载失败，使用占位符模型:', pmxError);
        
        // 如果PMX加载失败，使用占位符模型
        const modelData = await this.createPlaceholderModel(options);
        
        // 缓存模型
        this.modelCache.set(url, modelData);
        
        return modelData;
      }
    } catch (error) {
      console.error('PMX模型加载失败:', error);
      throw new Error(`PMX模型加载失败: ${(error as Error).message}`);
    }
  }

  /**
   * 加载真正的PMX文件
   */
  private async loadRealPMX(
    url: string,
    options: MMDLoadOptions
  ): Promise<MMDModelData> {
    console.log('🚀 开始加载真实PMX文件:', url);
    
    return new Promise((resolve, reject) => {
      // 直接使用MMDLoader加载PMX文件
      this.mmdLoader.load(
        url,
        (mmdModel: any) => {
          console.log('🎉 PMX模型加载成功!', mmdModel);
          console.log('模型详细信息:', {
            name: mmdModel.name || 'Unknown',
            children: mmdModel.children?.length || 0,
            geometry: mmdModel.geometry ? '有几何体' : '无几何体',
            material: mmdModel.material ? '有材质' : '无材质',
            animations: mmdModel.animations?.length || 0,
            skeleton: mmdModel.skeleton ? '有骨骼' : '无骨骼',
            morphTargetInfluences: mmdModel.morphTargetInfluences?.length || 0
          });

          // 设置默认位置和缩放
          mmdModel.position.set(0, -10, 0); // 调整Y位置，让模型在地面上
          mmdModel.scale.setScalar(1); // 保持原始大小

          // 应用用户选项
          if (options.scale) {
            mmdModel.scale.setScalar(options.scale);
            console.log('应用用户缩放:', options.scale);
          }
          
          if (options.position) {
            mmdModel.position.set(
              options.position.x,
              options.position.y,
              options.position.z
            );
            console.log('应用用户位置:', options.position);
          }
          
          if (options.rotation) {
            mmdModel.rotation.set(
              options.rotation.x,
              options.rotation.y,
              options.rotation.z
            );
            console.log('应用用户旋转:', options.rotation);
          }

          // 启用阴影和优化材质
          let meshCount = 0;
          let materialCount = 0;
          mmdModel.traverse((child: any) => {
            if (child.isMesh) {
              child.castShadow = true;
              child.receiveShadow = true;
              meshCount++;
              
              // 优化材质
              if (child.material) {
                if (Array.isArray(child.material)) {
                  child.material.forEach((mat: any) => {
                    if (mat.transparent === undefined) mat.transparent = true;
                    materialCount++;
                  });
                } else {
                  if (child.material.transparent === undefined) {
                    child.material.transparent = true;
                  }
                  materialCount++;
                }
              }
            }
          });

          console.log('模型处理完成:', {
            meshes: meshCount,
            materials: materialCount
          });

          // 提取模型资源
          const materials: THREE.Material[] = [];
          const textures: THREE.Texture[] = [];
          const bones: THREE.Bone[] = [];

          mmdModel.traverse((child: any) => {
            if (child.isMesh && child.material) {
              if (Array.isArray(child.material)) {
                materials.push(...child.material);
              } else {
                materials.push(child.material);
              }
            }
            if (child.isBone) {
              bones.push(child);
            }
          });

          // 创建包装组
          const group = new THREE.Group();
          group.add(mmdModel);
          group.name = 'KeqingModel';

          const modelData: MMDModelData = {
            model: group,
            materials,
            textures,
            bones,
            morphs: mmdModel.morphTargetInfluences || []
          };

          console.log('✅ PMX模型数据准备完成:', {
            materials: materials.length,
            bones: bones.length,
            morphs: modelData.morphs.length
          });
          
          resolve(modelData);
        },
        (progress: any) => {
          if (progress.lengthComputable) {
            const percent = Math.round((progress.loaded / progress.total) * 100);
            console.log(`📥 PMX加载进度: ${percent}% (${progress.loaded}/${progress.total} bytes)`);
          } else {
            console.log('📥 PMX加载进度:', progress.loaded, 'bytes');
          }
        },
        (error: any) => {
          console.error('❌ PMX加载失败:', {
            message: error.message,
            stack: error.stack,
            url: url,
            type: error.constructor.name
          });
          reject(new Error(`PMX加载失败: ${error.message || 'Unknown error'}`));
        }
      );
    });
  }

  /**
   * 创建刻晴风格的占位符模型
   */
  private async createPlaceholderModel(options: MMDLoadOptions): Promise<MMDModelData> {
    const defaultOptions: Required<MMDLoadOptions> = {
      enablePhysics: false,
      enableAnimation: true,
      scale: 1,
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0 },
      ...options
    };

    console.log('创建刻晴风格占位符模型...');
    
    // 创建一个更精致的刻晴风格人形占位符
    const group = new THREE.Group();
    
    // 刻晴的紫色主题色
    const keqingPurple = 0x6B46C1;
    const keqingLightPurple = 0x8B5CF6;
    const skinColor = 0xFFDBB5;
    const hairColor = 0x4C1D95;
    
    // 身体 - 使用刻晴的服装色彩
    const bodyGeometry = new THREE.CapsuleGeometry(0.4, 1.8, 8, 16);
    const bodyMaterial = new THREE.MeshLambertMaterial({ 
      color: keqingPurple,
      transparent: false,
      opacity: 1.0
    });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.8;
    group.add(body);

    // 头部 - 肤色
    const headGeometry = new THREE.SphereGeometry(0.35, 16, 16);
    const headMaterial = new THREE.MeshLambertMaterial({ 
      color: skinColor,
      transparent: false,
      opacity: 1.0
    });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 2.2;
    group.add(head);

    // 头发装饰 - 刻晴的发饰风格
    const hairGeometry = new THREE.SphereGeometry(0.38, 16, 16);
    const hairMaterial = new THREE.MeshLambertMaterial({ 
      color: hairColor,
      transparent: true,
      opacity: 0.8
    });
    const hair = new THREE.Mesh(hairGeometry, hairMaterial);
    hair.position.y = 2.3;
    hair.scale.set(1, 0.8, 1);
    group.add(hair);

    // 双马尾发型（简化版）
    const ponytailGeometry = new THREE.SphereGeometry(0.15, 8, 8);
    const ponytailMaterial = new THREE.MeshLambertMaterial({ 
      color: hairColor,
      transparent: true,
      opacity: 0.9
    });
    
    const leftPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
    leftPonytail.position.set(-0.4, 2.1, -0.2);
    leftPonytail.scale.set(1, 2, 1);
    group.add(leftPonytail);
    
    const rightPonytail = new THREE.Mesh(ponytailGeometry, ponytailMaterial);
    rightPonytail.position.set(0.4, 2.1, -0.2);
    rightPonytail.scale.set(1, 2, 1);
    group.add(rightPonytail);

    // 手臂 - 肤色
    const armGeometry = new THREE.CapsuleGeometry(0.15, 1.2, 6, 12);
    const armMaterial = new THREE.MeshLambertMaterial({ 
      color: skinColor,
      transparent: false,
      opacity: 1.0
    });
    
    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.6, 1.3, 0);
    leftArm.rotation.z = Math.PI / 8;
    group.add(leftArm);
    
    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    rightArm.position.set(0.6, 1.3, 0);
    rightArm.rotation.z = -Math.PI / 8;
    group.add(rightArm);

    // 袖子装饰
    const sleeveGeometry = new THREE.CylinderGeometry(0.18, 0.18, 0.3, 8);
    const sleeveMaterial = new THREE.MeshLambertMaterial({ 
      color: keqingLightPurple,
      transparent: false,
      opacity: 1.0
    });
    
    const leftSleeve = new THREE.Mesh(sleeveGeometry, sleeveMaterial);
    leftSleeve.position.set(-0.6, 1.8, 0);
    group.add(leftSleeve);
    
    const rightSleeve = new THREE.Mesh(sleeveGeometry, sleeveMaterial);
    rightSleeve.position.set(0.6, 1.8, 0);
    group.add(rightSleeve);

    // 腿部 - 刻晴的裤袜风格
    const legGeometry = new THREE.CapsuleGeometry(0.18, 1.4, 6, 12);
    const legMaterial = new THREE.MeshLambertMaterial({ 
      color: 0x2D1B69, // 深紫色裤袜
      transparent: false,
      opacity: 1.0
    });
    
    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
    leftLeg.position.set(-0.2, -0.3, 0);
    group.add(leftLeg);
    
    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
    rightLeg.position.set(0.2, -0.3, 0);
    group.add(rightLeg);

    // 鞋子
    const shoeGeometry = new THREE.BoxGeometry(0.25, 0.15, 0.4);
    const shoeMaterial = new THREE.MeshLambertMaterial({ 
      color: 0x1F1F1F, // 黑色鞋子
      transparent: false,
      opacity: 1.0
    });
    
    const leftShoe = new THREE.Mesh(shoeGeometry, shoeMaterial);
    leftShoe.position.set(-0.2, -1.1, 0.1);
    group.add(leftShoe);
    
    const rightShoe = new THREE.Mesh(shoeGeometry, shoeMaterial);
    rightShoe.position.set(0.2, -1.1, 0.1);
    group.add(rightShoe);

    // 添加一些装饰元素
    const gemGeometry = new THREE.SphereGeometry(0.08, 8, 8);
    const gemMaterial = new THREE.MeshLambertMaterial({ 
      color: 0xFFD700, // 金色宝石
      transparent: false,
      opacity: 1.0
    });
    
    const chestGem = new THREE.Mesh(gemGeometry, gemMaterial);
    chestGem.position.set(0, 1.4, 0.4);
    group.add(chestGem);

    // 应用变换
    group.scale.setScalar(defaultOptions.scale);
    group.position.set(
      defaultOptions.position.x,
      defaultOptions.position.y,
      defaultOptions.position.z
    );
    group.rotation.set(
      defaultOptions.rotation.x,
      defaultOptions.rotation.y,
      defaultOptions.rotation.z
    );

    // 启用阴影
    group.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        child.castShadow = true;
        child.receiveShadow = true;
      }
    });

    console.log('刻晴风格占位符模型创建完成');

    return {
      model: group,
      materials: [bodyMaterial, headMaterial, hairMaterial, armMaterial, sleeveMaterial, legMaterial, shoeMaterial, gemMaterial],
      textures: [],
      bones: [],
      morphs: []
    };
  }

  /**
   * 加载纹理
   */
  private async loadTexture(url: string): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      this.textureLoader.load(
        url,
        (texture) => {
          texture.wrapS = THREE.RepeatWrapping;
          texture.wrapT = THREE.RepeatWrapping;
          texture.flipY = false;
          resolve(texture);
        },
        (progress) => {
          console.log('纹理加载进度:', progress);
        },
        (error) => {
          console.error('纹理加载失败:', error);
          reject(error);
        }
      );
    });
  }

  /**
   * 预加载模型
   */
  public async preloadModel(url: string, options?: MMDLoadOptions): Promise<void> {
    try {
      await this.loadPMX(url, options);
      console.log('模型预加载完成:', url);
    } catch (error) {
      console.error('模型预加载失败:', error);
      throw error;
    }
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    // 清理模型资源
    this.modelCache.forEach((modelData) => {
      modelData.materials.forEach(material => material.dispose());
      modelData.textures.forEach(texture => texture.dispose());
      
      modelData.model.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          child.geometry.dispose();
        }
      });
    });
    
    this.modelCache.clear();
    console.log('MMD模型缓存已清除');
  }

  /**
   * 获取缓存信息
   */
  public getCacheInfo(): { count: number; urls: string[] } {
    return {
      count: this.modelCache.size,
      urls: Array.from(this.modelCache.keys())
    };
  }

  /**
   * 设置默认材质属性
   */
  public setDefaultMaterialProperties(properties: {
    transparent?: boolean;
    opacity?: number;
    side?: THREE.Side;
  }): void {
    // 这里可以设置默认的材质属性
    console.log('设置默认材质属性:', properties);
  }

  /**
   * 销毁加载器
   */
  public dispose(): void {
    this.clearCache();
    console.log('MMDLoader已销毁');
  }
}