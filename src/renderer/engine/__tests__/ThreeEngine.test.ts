import { ThreeEngine } from '../ThreeEngine';
import * as THREE from 'three';

// Mock Three.js
jest.mock('three', () => {
  const mockRenderer = {
    setSize: jest.fn(),
    setPixelRatio: jest.fn(),
    setClearColor: jest.fn(),
    render: jest.fn(),
    dispose: jest.fn(),
    domElement: document.createElement('canvas'),
    shadowMap: {
      enabled: false,
      type: null,
    },
    outputColorSpace: null,
    toneMapping: null,
    toneMappingExposure: 1,
    info: {
      render: { triangles: 100, calls: 5 },
      memory: { geometries: 3, textures: 2 },
    },
  };

  const mockCamera = {
    position: { set: jest.fn(), x: 0, y: 0, z: 5 },
    aspect: 1,
    updateProjectionMatrix: jest.fn(),
  };

  const mockScene = {
    add: jest.fn(),
    remove: jest.fn(),
    traverse: jest.fn(),
  };

  const mockClock = {
    start: jest.fn(),
    getDelta: jest.fn(() => 0.016), // ~60fps
  };

  const mockLight = {
    position: { set: jest.fn() },
    castShadow: false,
    shadow: {
      mapSize: { width: 0, height: 0 },
      camera: {
        near: 0,
        far: 0,
      },
    },
  };

  return {
    Scene: jest.fn(() => mockScene),
    PerspectiveCamera: jest.fn(() => mockCamera),
    WebGLRenderer: jest.fn(() => mockRenderer),
    Clock: jest.fn(() => mockClock),
    AmbientLight: jest.fn(() => mockLight),
    DirectionalLight: jest.fn(() => mockLight),
    BoxGeometry: jest.fn(),
    MeshLambertMaterial: jest.fn(),
    Mesh: jest.fn(() => ({
      geometry: { dispose: jest.fn() },
      material: { dispose: jest.fn() },
    })),
    PCFSoftShadowMap: 'PCFSoftShadowMap',
    SRGBColorSpace: 'SRGBColorSpace',
    ACESFilmicToneMapping: 'ACESFilmicToneMapping',
  };
});

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => {
  setTimeout(cb, 16);
  return 1;
});

global.cancelAnimationFrame = jest.fn();

// Mock performance.now
global.performance = {
  ...global.performance,
  now: jest.fn(() => Date.now()),
};

// Mock window.devicePixelRatio
Object.defineProperty(window, 'devicePixelRatio', {
  writable: true,
  value: 2,
});

// Mock document.hidden
Object.defineProperty(document, 'hidden', {
  writable: true,
  value: false,
});

describe('ThreeEngine', () => {
  let mockContainer: HTMLElement;
  let engine: ThreeEngine;

  beforeEach(() => {
    jest.clearAllMocks();
    mockContainer = document.createElement('div');
    
    // Mock clientWidth and clientHeight
    Object.defineProperty(mockContainer, 'clientWidth', {
      value: 800,
      writable: true,
    });
    Object.defineProperty(mockContainer, 'clientHeight', {
      value: 600,
      writable: true,
    });
    
    mockContainer.appendChild = jest.fn();
    mockContainer.removeChild = jest.fn();
  });

  afterEach(() => {
    if (engine) {
      engine.dispose();
    }
  });

  describe('WebGL支持检测', () => {
    it('应该检测WebGL支持', () => {
      // Mock canvas和WebGL context
      const mockCanvas = document.createElement('canvas');
      const mockContext = {
        getExtension: jest.fn(() => ({})),
      };
      
      jest.spyOn(document, 'createElement').mockReturnValue(mockCanvas as any);
      jest.spyOn(mockCanvas, 'getContext').mockReturnValue(mockContext as any);

      const result = ThreeEngine.checkWebGLSupport();
      
      expect(result.supported).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('应该检测WebGL不支持的情况', () => {
      const mockCanvas = document.createElement('canvas');
      
      jest.spyOn(document, 'createElement').mockReturnValue(mockCanvas as any);
      jest.spyOn(mockCanvas, 'getContext').mockReturnValue(null as any);

      const result = ThreeEngine.checkWebGLSupport();
      
      expect(result.supported).toBe(false);
      expect(result.error).toBe('WebGL不受支持');
    });
  });

  describe('引擎初始化', () => {
    it('应该使用默认配置初始化', () => {
      engine = new ThreeEngine(mockContainer);
      
      expect(THREE.Scene).toHaveBeenCalled();
      expect(THREE.PerspectiveCamera).toHaveBeenCalled();
      expect(THREE.WebGLRenderer).toHaveBeenCalledWith({
        antialias: true,
        alpha: true,
        powerPreference: 'high-performance',
        preserveDrawingBuffer: false,
        logarithmicDepthBuffer: false,
      });
    });

    it('应该使用自定义配置初始化', () => {
      const engineConfig = {
        antialias: false,
        powerPreference: 'low-power' as const,
      };
      
      const cameraConfig = {
        fov: 60,
        position: { x: 1, y: 2, z: 3 },
      };

      engine = new ThreeEngine(mockContainer, engineConfig, cameraConfig);
      
      expect(THREE.WebGLRenderer).toHaveBeenCalledWith(
        expect.objectContaining({
          antialias: false,
          powerPreference: 'low-power',
        })
      );
      
      expect(THREE.PerspectiveCamera).toHaveBeenCalledWith(60, expect.any(Number), 0.1, 1000);
    });

    it('应该设置光照系统', () => {
      engine = new ThreeEngine(mockContainer);
      
      expect(THREE.AmbientLight).toHaveBeenCalled();
      expect(THREE.DirectionalLight).toHaveBeenCalled();
    });

    it('应该将渲染器添加到容器', () => {
      engine = new ThreeEngine(mockContainer);
      
      expect(mockContainer.appendChild).toHaveBeenCalled();
    });
  });

  describe('渲染控制', () => {
    beforeEach(() => {
      engine = new ThreeEngine(mockContainer);
    });

    it('应该开始渲染循环', () => {
      engine.startRendering();
      
      expect(requestAnimationFrame).toHaveBeenCalled();
    });

    it('应该暂停渲染循环', () => {
      engine.startRendering();
      engine.pauseRendering();
      
      expect(cancelAnimationFrame).toHaveBeenCalled();
    });

    it('应该恢复渲染循环', () => {
      engine.startRendering();
      engine.pauseRendering();
      
      jest.clearAllMocks();
      engine.resumeRendering();
      
      expect(requestAnimationFrame).toHaveBeenCalled();
    });
  });

  describe('场景管理', () => {
    beforeEach(() => {
      engine = new ThreeEngine(mockContainer);
    });

    it('应该添加对象到场景', () => {
      const mockObject = {} as THREE.Object3D;
      const scene = engine.getScene();
      
      engine.addToScene(mockObject);
      
      expect(scene.add).toHaveBeenCalledWith(mockObject);
    });

    it('应该从场景移除对象', () => {
      const mockObject = {} as THREE.Object3D;
      const scene = engine.getScene();
      
      engine.removeFromScene(mockObject);
      
      expect(scene.remove).toHaveBeenCalledWith(mockObject);
    });
  });

  describe('性能监控', () => {
    beforeEach(() => {
      engine = new ThreeEngine(mockContainer);
    });

    it('应该返回渲染统计信息', () => {
      const stats = engine.getRenderStats();
      
      expect(stats).toEqual({
        fps: expect.any(Number),
        frameTime: expect.any(Number),
        triangles: 100,
        drawCalls: 5,
        memoryUsage: 5, // geometries + textures
      });
    });

    it('应该设置渲染回调', () => {
      const mockCallback = jest.fn();
      engine.setOnRender(mockCallback);
      
      // 这里我们无法直接测试回调是否被调用，因为它在动画循环中
      // 但我们可以验证回调被正确设置
      expect(engine).toBeDefined();
    });
  });

  describe('事件处理', () => {
    beforeEach(() => {
      engine = new ThreeEngine(mockContainer);
    });

    it('应该设置错误回调', () => {
      const mockCallback = jest.fn();
      engine.setOnError(mockCallback);
      
      expect(engine).toBeDefined();
    });

    it('应该设置大小变化回调', () => {
      const mockCallback = jest.fn();
      engine.setOnResize(mockCallback);
      
      expect(engine).toBeDefined();
    });
  });

  describe('获取器方法', () => {
    beforeEach(() => {
      engine = new ThreeEngine(mockContainer);
    });

    it('应该返回场景引用', () => {
      const scene = engine.getScene();
      expect(scene).toBeDefined();
    });

    it('应该返回相机引用', () => {
      const camera = engine.getCamera();
      expect(camera).toBeDefined();
    });

    it('应该返回渲染器引用', () => {
      const renderer = engine.getRenderer();
      expect(renderer).toBeDefined();
    });
  });

  describe('资源清理', () => {
    beforeEach(() => {
      engine = new ThreeEngine(mockContainer);
    });

    it('应该正确清理资源', () => {
      const renderer = engine.getRenderer();
      const scene = engine.getScene();
      
      // 先开始渲染循环，这样dispose时才会调用cancelAnimationFrame
      engine.startRendering();
      
      engine.dispose();
      
      expect(cancelAnimationFrame).toHaveBeenCalled();
      expect(scene.traverse).toHaveBeenCalled();
      expect(renderer.dispose).toHaveBeenCalled();
    });
  });
});