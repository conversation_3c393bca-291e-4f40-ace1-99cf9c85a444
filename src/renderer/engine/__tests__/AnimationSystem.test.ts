import * as THREE from 'three';
import { AnimationSystem, AnimationType, AnimationState } from '../AnimationSystem';

// Mock Three.js
jest.mock('three', () => ({
  ...jest.requireActual('three'),
  Clock: jest.fn().mockImplementation(() => ({
    getDelta: jest.fn().mockReturnValue(0.016) // 60 FPS
  })),
  AnimationMixer: jest.fn().mockImplementation(() => ({
    clipAction: jest.fn().mockReturnValue({
      setLoop: jest.fn(),
      fadeIn: jest.fn(),
      fadeOut: jest.fn(),
      reset: jest.fn(),
      play: jest.fn(),
      stop: jest.fn(),
      setEffectiveWeight: jest.fn()
    }),
    update: jest.fn(),
    stopAllAction: jest.fn(),
    addEventListener: jest.fn()
  })),
  AnimationClip: jest.fn(),
  VectorKeyframeTrack: jest.fn(),
  QuaternionKeyframeTrack: jest.fn(),
  LoopRepeat: 2201,
  LoopOnce: 2200
}));

describe('AnimationSystem', () => {
  let animationSystem: AnimationSystem;
  let mockModel: THREE.Group;

  beforeEach(() => {
    animationSystem = new AnimationSystem();
    mockModel = new THREE.Group();
    
    // 清除所有mock调用记录
    jest.clearAllMocks();
  });

  afterEach(() => {
    animationSystem.dispose();
  });

  describe('初始化', () => {
    test('应该正确初始化动画系统', () => {
      expect(animationSystem).toBeInstanceOf(AnimationSystem);
      expect(animationSystem.getAnimationState()).toBe(AnimationState.STOPPED);
      expect(animationSystem.getCurrentAnimation()).toBeNull();
    });

    test('应该正确初始化模型', () => {
      animationSystem.initialize(mockModel);
      
      expect(THREE.AnimationMixer).toHaveBeenCalledWith(mockModel);
    });
  });

  describe('动画播放', () => {
    beforeEach(() => {
      animationSystem.initialize(mockModel);
    });

    test('应该能够播放待机动画', () => {
      const result = animationSystem.playAnimation(AnimationType.IDLE);
      
      expect(result).toBe(true);
      expect(animationSystem.getCurrentAnimation()).toBe(AnimationType.IDLE);
    });

    test('应该能够播放说话动画', () => {
      const result = animationSystem.playAnimation(AnimationType.TALKING);
      
      expect(result).toBe(true);
      expect(animationSystem.getCurrentAnimation()).toBe(AnimationType.TALKING);
    });

    test('应该能够播放开心动画', () => {
      const result = animationSystem.playAnimation(AnimationType.HAPPY);
      
      expect(result).toBe(true);
      expect(animationSystem.getCurrentAnimation()).toBe(AnimationType.HAPPY);
    });

    test('应该能够播放惊讶动画', () => {
      const result = animationSystem.playAnimation(AnimationType.SURPRISED);
      
      expect(result).toBe(true);
      expect(animationSystem.getCurrentAnimation()).toBe(AnimationType.SURPRISED);
    });

    test('应该能够播放思考动画', () => {
      const result = animationSystem.playAnimation(AnimationType.THINKING);
      
      expect(result).toBe(true);
      expect(animationSystem.getCurrentAnimation()).toBe(AnimationType.THINKING);
    });

    test('应该能够播放挥手动画', () => {
      const result = animationSystem.playAnimation(AnimationType.WAVING);
      
      expect(result).toBe(true);
      expect(animationSystem.getCurrentAnimation()).toBe(AnimationType.WAVING);
    });

    test('播放不存在的动画应该返回false', () => {
      const result = animationSystem.playAnimation('nonexistent' as AnimationType);
      
      expect(result).toBe(false);
    });

    test('重复播放相同动画应该返回true', () => {
      animationSystem.playAnimation(AnimationType.IDLE);
      const result = animationSystem.playAnimation(AnimationType.IDLE);
      
      expect(result).toBe(true);
    });
  });

  describe('动画控制', () => {
    beforeEach(() => {
      animationSystem.initialize(mockModel);
    });

    test('应该能够停止指定动画', () => {
      animationSystem.playAnimation(AnimationType.IDLE);
      animationSystem.stopAnimation(AnimationType.IDLE);
      
      // 动画系统内部逻辑，无法直接验证停止状态
      expect(true).toBe(true);
    });

    test('应该能够停止所有动画', () => {
      animationSystem.playAnimation(AnimationType.IDLE);
      animationSystem.stopAnimation();
      
      expect(animationSystem.getCurrentAnimation()).toBeNull();
      expect(animationSystem.getAnimationState()).toBe(AnimationState.STOPPED);
    });

    test('应该能够暂停动画', () => {
      animationSystem.playAnimation(AnimationType.IDLE);
      animationSystem.pauseAnimation();
      
      expect(animationSystem.getAnimationState()).toBe(AnimationState.PAUSED);
    });

    test('应该能够恢复动画', () => {
      animationSystem.playAnimation(AnimationType.IDLE);
      animationSystem.pauseAnimation();
      animationSystem.resumeAnimation();
      
      expect(animationSystem.getAnimationState()).toBe(AnimationState.PLAYING);
    });
  });

  describe('动画配置', () => {
    beforeEach(() => {
      animationSystem.initialize(mockModel);
    });

    test('应该能够设置待机延迟时间', () => {
      const delay = 5000;
      animationSystem.setIdleDelay(delay);
      
      // 无法直接验证内部状态，但方法应该正常执行
      expect(true).toBe(true);
    });

    test('应该能够设置过渡持续时间', () => {
      const duration = 1.0;
      animationSystem.setTransitionDuration(duration);
      
      // 无法直接验证内部状态，但方法应该正常执行
      expect(true).toBe(true);
    });

    test('应该能够设置事件回调', () => {
      const callbacks = {
        onAnimationStart: jest.fn(),
        onAnimationEnd: jest.fn(),
        onAnimationLoop: jest.fn(),
        onTransitionStart: jest.fn(),
        onTransitionEnd: jest.fn()
      };

      animationSystem.setCallbacks(callbacks);
      
      // 无法直接验证回调设置，但方法应该正常执行
      expect(true).toBe(true);
    });
  });

  describe('动画查询', () => {
    beforeEach(() => {
      animationSystem.initialize(mockModel);
    });

    test('应该能够获取可用动画列表', () => {
      const animations = animationSystem.getAvailableAnimations();
      
      expect(animations).toContain(AnimationType.IDLE);
      expect(animations).toContain(AnimationType.TALKING);
      expect(animations).toContain(AnimationType.HAPPY);
      expect(animations).toContain(AnimationType.SURPRISED);
      expect(animations).toContain(AnimationType.THINKING);
      expect(animations).toContain(AnimationType.WAVING);
    });

    test('应该能够检查动画是否存在', () => {
      expect(animationSystem.hasAnimation(AnimationType.IDLE)).toBe(true);
      expect(animationSystem.hasAnimation(AnimationType.TALKING)).toBe(true);
      expect(animationSystem.hasAnimation('nonexistent' as AnimationType)).toBe(false);
    });

    test('应该能够获取当前动画类型', () => {
      expect(animationSystem.getCurrentAnimation()).toBeNull();
      
      animationSystem.playAnimation(AnimationType.IDLE);
      expect(animationSystem.getCurrentAnimation()).toBe(AnimationType.IDLE);
    });

    test('应该能够获取动画状态', () => {
      expect(animationSystem.getAnimationState()).toBe(AnimationState.STOPPED);
      
      animationSystem.playAnimation(AnimationType.IDLE);
      // 由于过渡时间，初始状态可能是TRANSITIONING
      const state = animationSystem.getAnimationState();
      expect([AnimationState.PLAYING, AnimationState.TRANSITIONING]).toContain(state);
    });
  });

  describe('动画更新', () => {
    beforeEach(() => {
      animationSystem.initialize(mockModel);
    });

    test('应该能够更新动画系统', () => {
      animationSystem.playAnimation(AnimationType.IDLE);
      
      // 调用update方法
      animationSystem.update();
      
      // 验证mixer.update被调用
      const mockMixer = (THREE.AnimationMixer as jest.Mock).mock.results[0].value;
      expect(mockMixer.update).toHaveBeenCalled();
    });

    test('暂停状态下不应该更新动画', () => {
      animationSystem.playAnimation(AnimationType.IDLE);
      animationSystem.pauseAnimation();
      
      // 清除之前的调用记录
      const mockMixer = (THREE.AnimationMixer as jest.Mock).mock.results[0].value;
      mockMixer.update.mockClear();
      
      // 调用update方法
      animationSystem.update();
      
      // 验证mixer.update没有被调用
      expect(mockMixer.update).not.toHaveBeenCalled();
    });
  });

  describe('资源清理', () => {
    test('应该能够正确销毁动画系统', () => {
      animationSystem.initialize(mockModel);
      animationSystem.playAnimation(AnimationType.IDLE);
      
      animationSystem.dispose();
      
      expect(animationSystem.getCurrentAnimation()).toBeNull();
      expect(animationSystem.getAnimationState()).toBe(AnimationState.STOPPED);
    });

    test('销毁后应该清理所有资源', () => {
      animationSystem.initialize(mockModel);
      
      const mockMixer = (THREE.AnimationMixer as jest.Mock).mock.results[0].value;
      
      animationSystem.dispose();
      
      expect(mockMixer.stopAllAction).toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    test('未初始化时播放动画应该返回false', () => {
      const result = animationSystem.playAnimation(AnimationType.IDLE);
      
      expect(result).toBe(false);
    });

    test('应该能够处理无效的动画类型', () => {
      animationSystem.initialize(mockModel);
      
      const result = animationSystem.playAnimation(null as any);
      
      expect(result).toBe(false);
    });
  });

  describe('动画过渡', () => {
    beforeEach(() => {
      animationSystem.initialize(mockModel);
    });

    test('应该能够在动画之间进行过渡', (done) => {
      // 播放第一个动画
      animationSystem.playAnimation(AnimationType.IDLE);
      
      // 设置过渡回调
      animationSystem.setCallbacks({
        onTransitionStart: (from, to) => {
          expect(from).toBe(AnimationType.IDLE);
          expect(to).toBe(AnimationType.HAPPY);
        },
        onTransitionEnd: (from, to) => {
          expect(from).toBe(AnimationType.IDLE);
          expect(to).toBe(AnimationType.HAPPY);
          done();
        }
      });
      
      // 播放第二个动画，触发过渡
      setTimeout(() => {
        animationSystem.playAnimation(AnimationType.HAPPY);
      }, 100);
    });

    test('过渡期间动画状态应该是TRANSITIONING', () => {
      animationSystem.playAnimation(AnimationType.IDLE);
      
      setTimeout(() => {
        animationSystem.playAnimation(AnimationType.HAPPY);
        expect(animationSystem.getAnimationState()).toBe(AnimationState.TRANSITIONING);
      }, 100);
    });
  });
});