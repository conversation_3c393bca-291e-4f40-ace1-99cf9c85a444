import { MMDLoader } from '../MMDLoader';
import * as THREE from 'three';

// Mock Three.js
jest.mock('three', () => {
  const mockGroup = {
    add: jest.fn(),
    position: { set: jest.fn() },
    rotation: { set: jest.fn() },
    scale: { setScalar: jest.fn() },
    traverse: jest.fn(),
  };

  const mockMesh = {
    position: { set: jest.fn(), y: 0 },
    rotation: { z: 0 },
    castShadow: false,
    receiveShadow: false,
  };

  const mockMaterial = {
    dispose: jest.fn(),
  };

  const mockTexture = {
    wrapS: null,
    wrapT: null,
    flipY: true,
    dispose: jest.fn(),
  };

  const mockGeometry = {
    dispose: jest.fn(),
  };

  const mockLoadingManager = {
    onLoad: null,
    onProgress: null,
    onError: null,
  };

  const mockFileLoader = {
    load: jest.fn(),
  };

  const mockTextureLoader = {
    load: jest.fn(),
  };

  return {
    Group: jest.fn(() => mockGroup),
    Mesh: jest.fn(() => mockMesh),
    CapsuleGeometry: jest.fn(() => mockGeometry),
    SphereGeometry: jest.fn(() => mockGeometry),
    MeshLambertMaterial: jest.fn(() => mockMaterial),
    LoadingManager: jest.fn(() => mockLoadingManager),
    FileLoader: jest.fn(() => mockFileLoader),
    TextureLoader: jest.fn(() => mockTextureLoader),
    RepeatWrapping: 'RepeatWrapping',
  };
});

describe('MMDLoader', () => {
  let loader: MMDLoader;

  beforeEach(() => {
    jest.clearAllMocks();
    loader = new MMDLoader();
  });

  afterEach(() => {
    if (loader) {
      loader.dispose();
    }
  });

  describe('初始化', () => {
    it('应该正确初始化加载器', () => {
      expect(loader).toBeDefined();
      expect(THREE.LoadingManager).toHaveBeenCalled();
      expect(THREE.FileLoader).toHaveBeenCalled();
      expect(THREE.TextureLoader).toHaveBeenCalled();
    });
  });

  describe('PMX模型加载', () => {
    it('应该成功加载PMX模型', async () => {
      const url = 'test-model.pmx';
      const options = {
        scale: 0.5,
        position: { x: 1, y: 2, z: 3 },
        rotation: { x: 0.1, y: 0.2, z: 0.3 },
      };

      const result = await loader.loadPMX(url, options);

      expect(result).toBeDefined();
      expect(result.model).toBeDefined();
      expect(result.materials).toBeDefined();
      expect(result.textures).toBeDefined();
      expect(result.bones).toBeDefined();
      expect(result.morphs).toBeDefined();

      expect(Array.isArray(result.materials)).toBe(true);
      expect(Array.isArray(result.textures)).toBe(true);
      expect(Array.isArray(result.bones)).toBe(true);
      expect(Array.isArray(result.morphs)).toBe(true);
    });

    it('应该使用默认选项加载模型', async () => {
      const url = 'test-model.pmx';
      
      const result = await loader.loadPMX(url);

      expect(result).toBeDefined();
      expect(result.model).toBeDefined();
    });

    it('应该从缓存加载已加载的模型', async () => {
      const url = 'cached-model.pmx';
      
      // 第一次加载
      const result1 = await loader.loadPMX(url);
      
      // 第二次加载应该从缓存获取
      const result2 = await loader.loadPMX(url);

      expect(result1).toBe(result2);
    });

    it('应该正确应用模型变换', async () => {
      const url = 'transform-model.pmx';
      const options = {
        scale: 2.0,
        position: { x: 10, y: 20, z: 30 },
        rotation: { x: 1, y: 2, z: 3 },
      };

      const result = await loader.loadPMX(url, options);
      
      expect(result.model.scale.setScalar).toHaveBeenCalledWith(2.0);
      expect(result.model.position.set).toHaveBeenCalledWith(10, 20, 30);
      expect(result.model.rotation.set).toHaveBeenCalledWith(1, 2, 3);
    });
  });

  describe('预加载功能', () => {
    it('应该成功预加载模型', async () => {
      const url = 'preload-model.pmx';
      
      await expect(loader.preloadModel(url)).resolves.not.toThrow();
    });

    it('预加载失败时应该抛出错误', async () => {
      const url = 'invalid-model.pmx';
      
      // Mock加载失败
      jest.spyOn(loader, 'loadPMX').mockRejectedValue(new Error('加载失败'));
      
      await expect(loader.preloadModel(url)).rejects.toThrow();
    });
  });

  describe('缓存管理', () => {
    it('应该返回正确的缓存信息', async () => {
      const url1 = 'model1.pmx';
      const url2 = 'model2.pmx';
      
      await loader.loadPMX(url1);
      await loader.loadPMX(url2);
      
      const cacheInfo = loader.getCacheInfo();
      
      expect(cacheInfo.count).toBe(2);
      expect(cacheInfo.urls).toContain(url1);
      expect(cacheInfo.urls).toContain(url2);
    });

    it('应该正确清除缓存', async () => {
      const url = 'cache-test.pmx';
      
      await loader.loadPMX(url);
      
      let cacheInfo = loader.getCacheInfo();
      expect(cacheInfo.count).toBe(1);
      
      loader.clearCache();
      
      cacheInfo = loader.getCacheInfo();
      expect(cacheInfo.count).toBe(0);
    });
  });

  describe('材质设置', () => {
    it('应该设置默认材质属性', () => {
      const properties = {
        transparent: true,
        opacity: 0.8,
      };
      
      expect(() => loader.setDefaultMaterialProperties(properties)).not.toThrow();
    });
  });

  describe('资源清理', () => {
    it('应该正确销毁加载器', async () => {
      const url = 'dispose-test.pmx';
      
      await loader.loadPMX(url);
      
      expect(() => loader.dispose()).not.toThrow();
      
      const cacheInfo = loader.getCacheInfo();
      expect(cacheInfo.count).toBe(0);
    });
  });

  describe('错误处理', () => {
    it('应该处理加载错误', async () => {
      // 这里可以测试各种错误情况
      // 由于当前实现使用占位符模型，暂时跳过
      expect(true).toBe(true);
    });
  });
});