import * as THREE from 'three';
import { VMDLoader, VMDAnimationData, VMDLoadOptions } from '../VMDLoader';

// Mock Three.js
jest.mock('three', () => ({
  ...jest.requireActual('three'),
  Vector3: jest.fn().mockImplementation((x = 0, y = 0, z = 0) => ({ x, y, z })),
  Quaternion: jest.fn().mockImplementation((x = 0, y = 0, z = 0, w = 1) => ({ x, y, z, w, setFromEuler: jest.fn().mockReturnThis() })),
  Euler: jest.fn().mockImplementation((x = 0, y = 0, z = 0) => ({ x, y, z })),
  AnimationClip: jest.fn().mockImplementation((name, duration, tracks) => ({ name, duration, tracks })),
  VectorKeyframeTrack: jest.fn().mockImplementation((name, times, values) => ({ name, times, values })),
  QuaternionKeyframeTrack: jest.fn().mockImplementation((name, times, values) => ({ name, times, values })),
  NumberKeyframeTrack: jest.fn().mockImplementation((name, times, values) => ({ name, times, values })),
  LoopOnce: 2200,
  LoopRepeat: 2201
}));

describe('VMDLoader', () => {
  let vmdLoader: VMDLoader;

  beforeEach(() => {
    vmdLoader = new VMDLoader();
    jest.clearAllMocks();
  });

  afterEach(() => {
    vmdLoader.dispose();
  });

  describe('初始化', () => {
    test('应该正确初始化VMDLoader', () => {
      expect(vmdLoader).toBeInstanceOf(VMDLoader);
      expect(vmdLoader.getFrameRate()).toBe(30);
    });

    test('应该能够设置帧率', () => {
      vmdLoader.setFrameRate(60);
      expect(vmdLoader.getFrameRate()).toBe(60);
    });
  });

  describe('VMD文件加载', () => {
    test('应该能够加载VMD文件', async () => {
      const url = 'test.vmd';
      const options: VMDLoadOptions = {
        frameRate: 30,
        enableBoneAnimation: true,
        enableMorphAnimation: true
      };

      const result = await vmdLoader.loadVMD(url, options);

      expect(result).toBeDefined();
      expect(result.bones).toBeDefined();
      expect(result.morphs).toBeDefined();
      expect(result.frameRate).toBe(30);
      expect(result.duration).toBe(3.0);
    });

    test('应该能够使用默认选项加载VMD文件', async () => {
      const url = 'test.vmd';
      
      const result = await vmdLoader.loadVMD(url);

      expect(result).toBeDefined();
      expect(result.frameRate).toBe(30);
    });

    test('加载失败时应该抛出错误', async () => {
      // 模拟加载失败
      const originalCreateMock = vmdLoader['createMockVMDData'];
      vmdLoader['createMockVMDData'] = jest.fn().mockRejectedValue(new Error('加载失败'));

      await expect(vmdLoader.loadVMD('invalid.vmd')).rejects.toThrow('VMD文件加载失败');
    });
  });

  describe('批量加载', () => {
    test('应该能够批量加载多个VMD文件', async () => {
      const urls = ['test1.vmd', 'test2.vmd', 'test3.vmd'];
      
      const results = await vmdLoader.loadMultipleVMD(urls);

      expect(results.size).toBe(3);
      expect(results.has('test1.vmd')).toBe(true);
      expect(results.has('test2.vmd')).toBe(true);
      expect(results.has('test3.vmd')).toBe(true);
    });

    test('批量加载时部分失败不应该影响其他文件', async () => {
      const urls = ['test1.vmd', 'invalid.vmd', 'test3.vmd'];
      
      // 模拟第二个文件加载失败
      const originalLoadVMD = vmdLoader.loadVMD;
      vmdLoader.loadVMD = jest.fn().mockImplementation((url) => {
        if (url === 'invalid.vmd') {
          return Promise.reject(new Error('加载失败'));
        }
        return originalLoadVMD.call(vmdLoader, url);
      });

      const results = await vmdLoader.loadMultipleVMD(urls);

      expect(results.size).toBe(2);
      expect(results.has('test1.vmd')).toBe(true);
      expect(results.has('invalid.vmd')).toBe(false);
      expect(results.has('test3.vmd')).toBe(true);
    });
  });

  describe('动画剪辑转换', () => {
    test('应该能够将VMD数据转换为AnimationClip', async () => {
      const vmdData = await vmdLoader.loadVMD('test.vmd');
      
      const clip = vmdLoader.convertToAnimationClip(vmdData, 'test_clip');

      expect(THREE.AnimationClip).toHaveBeenCalledWith('test_clip', 3.0, expect.any(Array));
      expect(clip).toBeDefined();
    });

    test('应该为骨骼动画创建正确的轨道', async () => {
      const vmdData = await vmdLoader.loadVMD('test.vmd');
      
      vmdLoader.convertToAnimationClip(vmdData);

      // 验证创建了位置和旋转轨道
      expect(THREE.VectorKeyframeTrack).toHaveBeenCalled();
      expect(THREE.QuaternionKeyframeTrack).toHaveBeenCalled();
    });

    test('应该为表情动画创建正确的轨道', async () => {
      const vmdData = await vmdLoader.loadVMD('test.vmd');
      
      vmdLoader.convertToAnimationClip(vmdData);

      // 验证创建了数值轨道
      expect(THREE.NumberKeyframeTrack).toHaveBeenCalled();
    });

    test('应该使用默认剪辑名称', async () => {
      const vmdData = await vmdLoader.loadVMD('test.vmd');
      
      vmdLoader.convertToAnimationClip(vmdData);

      expect(THREE.AnimationClip).toHaveBeenCalledWith('vmd_animation', 3.0, expect.any(Array));
    });
  });

  describe('动画动作创建', () => {
    test('应该能够创建动画动作', async () => {
      const mockMixer = {
        clipAction: jest.fn().mockReturnValue({
          setLoop: jest.fn(),
          clampWhenFinished: false
        })
      } as any;

      const vmdData = await vmdLoader.loadVMD('test.vmd');
      
      const action = vmdLoader.createAnimationAction(mockMixer, vmdData, 'test_action');

      expect(mockMixer.clipAction).toHaveBeenCalled();
      expect(action.setLoop).toHaveBeenCalledWith(THREE.LoopOnce, 1);
      expect(action.clampWhenFinished).toBe(true);
    });

    test('应该使用默认动作名称', async () => {
      const mockMixer = {
        clipAction: jest.fn().mockReturnValue({
          setLoop: jest.fn(),
          clampWhenFinished: false
        })
      } as any;

      const vmdData = await vmdLoader.loadVMD('test.vmd');
      
      vmdLoader.createAnimationAction(mockMixer, vmdData);

      expect(mockMixer.clipAction).toHaveBeenCalled();
    });
  });

  describe('VMD文件验证', () => {
    test('应该能够验证有效的VMD文件', () => {
      // 创建一个模拟的VMD文件缓冲区
      const buffer = new ArrayBuffer(100);
      const view = new DataView(buffer);
      
      // 写入VMD签名
      const signature = 'Vocaloid Motion Data 0002';
      for (let i = 0; i < signature.length; i++) {
        view.setUint8(i, signature.charCodeAt(i));
      }

      const isValid = vmdLoader.validateVMDFile(buffer);

      expect(isValid).toBe(true);
    });

    test('应该能够识别无效的VMD文件', () => {
      // 创建一个无效的文件缓冲区
      const buffer = new ArrayBuffer(100);
      
      const isValid = vmdLoader.validateVMDFile(buffer);

      expect(isValid).toBe(false);
    });

    test('验证损坏的文件时应该返回false', () => {
      // 创建一个太小的缓冲区
      const buffer = new ArrayBuffer(10);
      
      const isValid = vmdLoader.validateVMDFile(buffer);

      expect(isValid).toBe(false);
    });
  });

  describe('VMD文件信息', () => {
    test('应该能够获取VMD文件信息', () => {
      // 创建一个模拟的VMD文件缓冲区
      const buffer = new ArrayBuffer(200);
      const view = new DataView(buffer);
      
      // 写入VMD签名
      const signature = 'Vocaloid Motion Data 0002';
      for (let i = 0; i < signature.length; i++) {
        view.setUint8(i, signature.charCodeAt(i));
      }
      
      // 写入模型名称
      const modelName = 'TestModel';
      for (let i = 0; i < modelName.length; i++) {
        view.setUint8(30 + i, modelName.charCodeAt(i));
      }
      
      // 写入骨骼关键帧数量
      view.setUint32(50, 100, true);
      
      // 写入表情关键帧数量（假设偏移）
      view.setUint32(54 + 100 * 111, 50, true);

      const info = vmdLoader.getVMDInfo(buffer);

      expect(info).toBeDefined();
      expect(info!.signature).toContain('Vocaloid Motion Data');
      expect(info!.boneKeyframeCount).toBe(100);
    });

    test('获取损坏文件信息时应该返回null', () => {
      const buffer = new ArrayBuffer(10);
      
      const info = vmdLoader.getVMDInfo(buffer);

      expect(info).toBeNull();
    });
  });

  describe('资源清理', () => {
    test('应该能够正确销毁加载器', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      vmdLoader.dispose();

      expect(consoleSpy).toHaveBeenCalledWith('VMDLoader已销毁');
      
      consoleSpy.mockRestore();
    });
  });

  describe('错误处理', () => {
    test('应该能够处理空URL', async () => {
      await expect(vmdLoader.loadVMD('')).rejects.toThrow();
    });

    test('应该能够处理无效选项', async () => {
      const options = {
        frameRate: -1,
        scale: 0
      };

      // 应该使用默认值而不是抛出错误
      const result = await vmdLoader.loadVMD('test.vmd', options);
      expect(result).toBeDefined();
    });
  });

  describe('模拟数据生成', () => {
    test('应该生成包含骨骼动画的模拟数据', async () => {
      const vmdData = await vmdLoader.loadVMD('test.vmd');

      expect(vmdData.bones).toBeDefined();
      expect(vmdData.bones.length).toBeGreaterThan(0);
      
      // 检查头部动画
      const headBone = vmdData.bones.find(bone => bone.boneName === 'head');
      expect(headBone).toBeDefined();
      expect(headBone!.keyframes.length).toBeGreaterThan(0);
      
      // 检查身体动画
      const bodyBone = vmdData.bones.find(bone => bone.boneName === 'body');
      expect(bodyBone).toBeDefined();
      expect(bodyBone!.keyframes.length).toBeGreaterThan(0);
    });

    test('应该生成包含表情动画的模拟数据', async () => {
      const vmdData = await vmdLoader.loadVMD('test.vmd');

      expect(vmdData.morphs).toBeDefined();
      expect(vmdData.morphs.length).toBeGreaterThan(0);
      
      // 检查眨眼动画
      const blinkMorph = vmdData.morphs.find(morph => morph.morphName === 'blink');
      expect(blinkMorph).toBeDefined();
      expect(blinkMorph!.keyframes.length).toBeGreaterThan(0);
      
      // 检查微笑动画
      const smileMorph = vmdData.morphs.find(morph => morph.morphName === 'smile');
      expect(smileMorph).toBeDefined();
      expect(smileMorph!.keyframes.length).toBeGreaterThan(0);
    });

    test('应该生成正确的时间和持续时间', async () => {
      const vmdData = await vmdLoader.loadVMD('test.vmd', { frameRate: 60 });

      expect(vmdData.frameRate).toBe(60);
      expect(vmdData.duration).toBe(3.0);
    });
  });
});