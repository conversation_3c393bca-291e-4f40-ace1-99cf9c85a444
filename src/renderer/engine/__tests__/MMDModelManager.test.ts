import { MMDModelManager } from '../MMDModelManager';
import { MMDLoader, MMDModelData } from '../MMDLoader';

// Mock MMDLoader
jest.mock('../MMDLoader', () => {
  const mockModelData: MMDModelData = {
    model: {} as any,
    materials: [],
    textures: [],
    bones: [],
    morphs: [],
  };

  return {
    MMDLoader: jest.fn().mockImplementation(() => ({
      loadPMX: jest.fn().mockResolvedValue(mockModelData),
      preloadModel: jest.fn().mockResolvedValue(undefined),
      getCacheInfo: jest.fn().mockReturnValue({ count: 0, urls: [] }),
      clearCache: jest.fn(),
      dispose: jest.fn(),
    })),
  };
});

describe('MMDModelManager', () => {
  let manager: MMDModelManager;
  let mockLoader: jest.Mocked<MMDLoader>;

  beforeEach(() => {
    jest.clearAllMocks();
    manager = new MMDModelManager({
      maxCacheSize: 3,
      enablePreloading: true,
      retryAttempts: 2,
      retryDelay: 100,
    });
    
    // 获取mock的loader实例
    mockLoader = (MMDLoader as jest.MockedClass<typeof MMDLoader>).mock.instances[0] as jest.Mocked<MMDLoader>;
  });

  afterEach(() => {
    if (manager) {
      manager.dispose();
    }
  });

  describe('初始化', () => {
    it('应该正确初始化管理器', () => {
      expect(manager).toBeDefined();
      expect(MMDLoader).toHaveBeenCalled();
    });

    it('应该使用默认配置', () => {
      const defaultManager = new MMDModelManager();
      expect(defaultManager).toBeDefined();
    });
  });

  describe('模型加载', () => {
    it('应该成功加载模型', async () => {
      const url = 'test-model.pmx';
      const options = { scale: 0.5 };

      const result = await manager.loadModel(url, options);

      expect(result.success).toBe(true);
      expect(result.model).toBeDefined();
      expect(result.error).toBeUndefined();
      expect(mockLoader.loadPMX).toHaveBeenCalledWith(url, options);
    });

    it('应该处理加载失败', async () => {
      const url = 'invalid-model.pmx';
      const errorMessage = '模型不存在';
      
      mockLoader.loadPMX.mockRejectedValue(new Error(errorMessage));

      const result = await manager.loadModel(url);

      expect(result.success).toBe(false);
      expect(result.error).toContain(errorMessage);
      expect(result.model).toBeUndefined();
    });

    it('应该避免重复加载同一模型', async () => {
      const url = 'duplicate-model.pmx';

      // 同时发起两个加载请求
      const promise1 = manager.loadModel(url);
      const promise2 = manager.loadModel(url);

      const [result1, result2] = await Promise.all([promise1, promise2]);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      expect(result1.model).toBe(result2.model);
      
      // 应该只调用一次loadPMX
      expect(mockLoader.loadPMX).toHaveBeenCalledTimes(1);
    });

    it('应该支持重试机制', async () => {
      const url = 'retry-model.pmx';
      
      // 前两次失败，第三次成功
      mockLoader.loadPMX
        .mockRejectedValueOnce(new Error('第一次失败'))
        .mockRejectedValueOnce(new Error('第二次失败'))
        .mockResolvedValueOnce({
          model: {} as any,
          materials: [],
          textures: [],
          bones: [],
          morphs: [],
        });

      const result = await manager.loadModel(url);

      expect(result.success).toBe(true);
      expect(mockLoader.loadPMX).toHaveBeenCalledTimes(3);
    });

    it('应该在重试次数用完后返回失败', async () => {
      const url = 'always-fail-model.pmx';
      
      mockLoader.loadPMX.mockRejectedValue(new Error('持续失败'));

      const result = await manager.loadModel(url);

      expect(result.success).toBe(false);
      expect(mockLoader.loadPMX).toHaveBeenCalledTimes(3); // 初始 + 2次重试
    });
  });

  describe('预加载功能', () => {
    it('应该成功预加载模型', async () => {
      const url = 'preload-model.pmx';
      
      const result = await manager.preloadModel(url);

      expect(result).toBe(true);
      expect(mockLoader.preloadModel).toHaveBeenCalledWith(url, undefined);
    });

    it('预加载失败时应该返回false', async () => {
      const url = 'preload-fail-model.pmx';
      
      mockLoader.preloadModel.mockRejectedValue(new Error('预加载失败'));
      
      const result = await manager.preloadModel(url);

      expect(result).toBe(false);
    });

    it('应该支持批量预加载', async () => {
      const models = [
        { url: 'model1.pmx', options: { scale: 1 } },
        { url: 'model2.pmx', options: { scale: 2 } },
        { url: 'model3.pmx' },
      ];

      const result = await manager.preloadModels(models);

      expect(result.success).toBe(3);
      expect(result.failed).toBe(0);
      expect(mockLoader.preloadModel).toHaveBeenCalledTimes(3);
    });

    it('批量预加载应该处理部分失败', async () => {
      const models = [
        { url: 'success-model.pmx' },
        { url: 'fail-model.pmx' },
      ];

      mockLoader.preloadModel
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('失败'));

      const result = await manager.preloadModels(models);

      expect(result.success).toBe(1);
      expect(result.failed).toBe(1);
    });
  });

  describe('加载状态管理', () => {
    it('应该正确报告加载状态', async () => {
      const url = 'status-model.pmx';
      
      // 延迟解析以测试加载状态
      let resolveLoad: (value: any) => void;
      const loadPromise = new Promise<MMDModelData>(resolve => {
        resolveLoad = resolve;
      });
      mockLoader.loadPMX.mockReturnValue(loadPromise);

      // 开始加载
      const loadModelPromise = manager.loadModel(url);
      
      // 检查加载状态
      const status = manager.getLoadingStatus();
      expect(status.isLoading).toBe(true);
      expect(status.loadingCount).toBe(1);
      expect(status.loadingUrls).toContain(url);

      // 完成加载
      resolveLoad!({
        model: {} as any,
        materials: [],
        textures: [],
        bones: [],
        morphs: [],
      });
      
      await loadModelPromise;
      
      // 检查加载完成后的状态
      const finalStatus = manager.getLoadingStatus();
      expect(finalStatus.isLoading).toBe(false);
      expect(finalStatus.loadingCount).toBe(0);
    });

    it('应该支持取消加载', () => {
      const url = 'cancel-model.pmx';
      
      // 开始加载但不等待完成
      manager.loadModel(url);
      
      const cancelled = manager.cancelLoad(url);
      expect(cancelled).toBe(true);
      
      const status = manager.getLoadingStatus();
      expect(status.loadingCount).toBe(0);
    });

    it('应该支持取消所有加载', () => {
      const urls = ['model1.pmx', 'model2.pmx', 'model3.pmx'];
      
      // 开始多个加载
      urls.forEach(url => manager.loadModel(url));
      
      manager.cancelAllLoads();
      
      const status = manager.getLoadingStatus();
      expect(status.loadingCount).toBe(0);
    });
  });

  describe('事件回调', () => {
    it('应该设置和触发加载回调', async () => {
      const callbacks = {
        onLoadStart: jest.fn(),
        onLoadProgress: jest.fn(),
        onLoadComplete: jest.fn(),
        onLoadError: jest.fn(),
      };

      manager.setLoadCallbacks(callbacks);

      const url = 'callback-model.pmx';
      await manager.loadModel(url);

      expect(callbacks.onLoadStart).toHaveBeenCalledWith(url);
      expect(callbacks.onLoadComplete).toHaveBeenCalled();
    });

    it('应该在加载失败时触发错误回调', async () => {
      const onLoadError = jest.fn();
      manager.setLoadCallbacks({ onLoadError });

      const url = 'error-model.pmx';
      mockLoader.loadPMX.mockRejectedValue(new Error('加载失败'));

      await manager.loadModel(url);

      expect(onLoadError).toHaveBeenCalled();
    });
  });

  describe('缓存管理', () => {
    it('应该获取缓存信息', () => {
      const cacheInfo = manager.getCacheInfo();
      
      expect(mockLoader.getCacheInfo).toHaveBeenCalled();
      expect(cacheInfo).toBeDefined();
    });

    it('应该清除缓存', () => {
      manager.clearCache();
      
      expect(mockLoader.clearCache).toHaveBeenCalled();
    });
  });

  describe('默认模型', () => {
    it('应该获取默认模型', () => {
      const defaultModel = manager.getDefaultModel();
      
      // 由于默认模型是异步初始化的，这里可能为null
      expect(defaultModel).toBeDefined();
    });
  });

  describe('资源清理', () => {
    it('应该正确销毁管理器', () => {
      expect(() => manager.dispose()).not.toThrow();
      
      expect(mockLoader.dispose).toHaveBeenCalled();
      
      const status = manager.getLoadingStatus();
      expect(status.loadingCount).toBe(0);
    });
  });
});