import { AnimationStateManager, AnimationStateData, AnimationQueueItem } from '../AnimationStateManager';
import { AnimationType, AnimationState, AnimationConfig } from '../AnimationSystem';

// Mock setTimeout and clearTimeout
jest.useFakeTimers();

describe('AnimationStateManager', () => {
  let stateManager: AnimationStateManager;

  beforeEach(() => {
    stateManager = new AnimationStateManager();
    jest.clearAllTimers();
  });

  afterEach(() => {
    stateManager.dispose();
    jest.clearAllTimers();
  });

  describe('初始化', () => {
    test('应该正确初始化状态管理器', () => {
      expect(stateManager).toBeInstanceOf(AnimationStateManager);
      expect(stateManager.getAllCurrentStates()).toHaveLength(0);
      expect(stateManager.getCurrentHighestPriorityState()).toBeNull();
    });

    test('应该设置默认优先级', () => {
      expect(stateManager.getPriority(AnimationType.IDLE)).toBe(1);
      expect(stateManager.getPriority(AnimationType.TALKING)).toBe(6);
      expect(stateManager.getPriority(AnimationType.SURPRISED)).toBe(5);
    });
  });

  describe('动画状态管理', () => {
    test('应该能够添加动画状态', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2,
        loop: true
      };

      const result = stateManager.addAnimationState(AnimationType.IDLE, config);

      expect(result).toBe(true);
      expect(stateManager.getAllCurrentStates()).toHaveLength(1);
      
      const state = stateManager.getAnimationState(AnimationType.IDLE);
      expect(state).toBeDefined();
      expect(state!.type).toBe(AnimationType.IDLE);
      expect(state!.state).toBe(AnimationState.PLAYING);
    });

    test('应该能够移除动画状态', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addAnimationState(AnimationType.IDLE, config);
      const result = stateManager.removeAnimationState(AnimationType.IDLE);

      expect(result).toBe(true);
      expect(stateManager.getAllCurrentStates()).toHaveLength(0);
      expect(stateManager.getAnimationState(AnimationType.IDLE)).toBeNull();
    });

    test('移除不存在的动画状态应该返回false', () => {
      const result = stateManager.removeAnimationState(AnimationType.IDLE);
      expect(result).toBe(false);
    });

    test('应该能够更新动画状态', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addAnimationState(AnimationType.IDLE, config);
      
      const result = stateManager.updateAnimationState(AnimationType.IDLE, {
        progress: 0.5,
        weight: 0.8
      });

      expect(result).toBe(true);
      
      const state = stateManager.getAnimationState(AnimationType.IDLE);
      expect(state!.progress).toBe(0.5);
      expect(state!.weight).toBe(0.8);
    });

    test('更新不存在的动画状态应该返回false', () => {
      const result = stateManager.updateAnimationState(AnimationType.IDLE, {
        progress: 0.5
      });

      expect(result).toBe(false);
    });
  });

  describe('优先级管理', () => {
    test('应该根据优先级决定是否可以播放动画', () => {
      // 添加低优先级动画
      const idleConfig: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };
      stateManager.addAnimationState(AnimationType.IDLE, idleConfig);

      // 尝试播放高优先级动画
      expect(stateManager.canPlayAnimation(AnimationType.TALKING)).toBe(true);
      
      // 尝试播放低优先级动画
      expect(stateManager.canPlayAnimation(AnimationType.THINKING)).toBe(false);
    });

    test('应该能够设置和获取优先级', () => {
      stateManager.setPriority(AnimationType.IDLE, 10);
      expect(stateManager.getPriority(AnimationType.IDLE)).toBe(10);
    });

    test('应该能够获取当前最高优先级状态', () => {
      const idleConfig: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };
      const talkingConfig: AnimationConfig = {
        type: AnimationType.TALKING,
        duration: 3
      };

      stateManager.addAnimationState(AnimationType.IDLE, idleConfig);
      stateManager.addAnimationState(AnimationType.TALKING, talkingConfig);

      const highestPriorityState = stateManager.getCurrentHighestPriorityState();
      expect(highestPriorityState).toBeDefined();
      expect(highestPriorityState!.type).toBe(AnimationType.TALKING);
    });

    test('应该能够强制播放动画', () => {
      // 添加高优先级动画
      const talkingConfig: AnimationConfig = {
        type: AnimationType.TALKING,
        duration: 3
      };
      stateManager.addAnimationState(AnimationType.TALKING, talkingConfig);

      // 强制播放低优先级动画
      const idleConfig: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };
      stateManager.forcePlayAnimation(AnimationType.IDLE, idleConfig);

      expect(stateManager.getAllCurrentStates()).toHaveLength(1);
      expect(stateManager.getAnimationState(AnimationType.IDLE)).toBeDefined();
      expect(stateManager.getAnimationState(AnimationType.TALKING)).toBeNull();
    });
  });

  describe('动画队列管理', () => {
    test('应该能够添加动画到队列', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addToQueue(AnimationType.IDLE, config);

      const queueStatus = stateManager.getQueueStatus();
      expect(queueStatus.length).toBe(1);
      expect(queueStatus.nextAnimation).toBe(AnimationType.IDLE);
    });

    test('应该按优先级排序队列', () => {
      const idleConfig: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };
      const talkingConfig: AnimationConfig = {
        type: AnimationType.TALKING,
        duration: 3
      };

      stateManager.addToQueue(AnimationType.IDLE, idleConfig);
      stateManager.addToQueue(AnimationType.TALKING, talkingConfig);

      const queueStatus = stateManager.getQueueStatus();
      expect(queueStatus.nextAnimation).toBe(AnimationType.TALKING);
    });

    test('应该能够清空队列', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addToQueue(AnimationType.IDLE, config);
      stateManager.clearQueue();

      const queueStatus = stateManager.getQueueStatus();
      expect(queueStatus.length).toBe(0);
      expect(queueStatus.nextAnimation).toBeNull();
    });

    test('应该能够处理队列中的延迟播放', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };
      const callback = jest.fn();

      stateManager.addToQueue(AnimationType.IDLE, config, 1000, callback);

      // 快进时间
      jest.advanceTimersByTime(1000);

      expect(callback).toHaveBeenCalled();
    });
  });

  describe('状态控制', () => {
    test('应该能够停止所有动画', () => {
      const idleConfig: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };
      const talkingConfig: AnimationConfig = {
        type: AnimationType.TALKING,
        duration: 3
      };

      stateManager.addAnimationState(AnimationType.IDLE, idleConfig);
      stateManager.addAnimationState(AnimationType.TALKING, talkingConfig);
      stateManager.addToQueue(AnimationType.HAPPY, { type: AnimationType.HAPPY });

      stateManager.stopAllAnimations();

      expect(stateManager.getAllCurrentStates()).toHaveLength(0);
      expect(stateManager.getQueueStatus().length).toBe(0);
    });

    test('应该能够暂停所有动画', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addAnimationState(AnimationType.IDLE, config);
      stateManager.pauseAllAnimations();

      const state = stateManager.getAnimationState(AnimationType.IDLE);
      expect(state!.state).toBe(AnimationState.PAUSED);
    });

    test('应该能够恢复所有动画', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addAnimationState(AnimationType.IDLE, config);
      stateManager.pauseAllAnimations();
      stateManager.resumeAllAnimations();

      const state = stateManager.getAnimationState(AnimationType.IDLE);
      expect(state!.state).toBe(AnimationState.PLAYING);
    });
  });

  describe('自动状态管理', () => {
    test('应该能够启用和禁用自动状态管理', () => {
      stateManager.setAutoStateEnabled(false);
      
      // 快进时间超过空闲超时
      jest.advanceTimersByTime(35000);

      // 应该没有自动添加待机动画
      expect(stateManager.getAnimationState(AnimationType.IDLE)).toBeNull();
    });

    test('应该能够设置空闲超时时间', () => {
      stateManager.setIdleTimeout(5000);
      
      // 快进时间
      jest.advanceTimersByTime(6000);

      // 应该自动添加待机动画
      expect(stateManager.getAnimationState(AnimationType.IDLE)).toBeDefined();
    });

    test('应该能够记录活动时间', () => {
      stateManager.recordActivity();
      
      // 快进时间但不超过默认超时
      jest.advanceTimersByTime(25000);

      // 应该没有自动添加待机动画
      expect(stateManager.getAnimationState(AnimationType.IDLE)).toBeNull();
    });
  });

  describe('事件回调', () => {
    test('应该能够设置状态变化回调', () => {
      const onStateChange = jest.fn();
      stateManager.setOnStateChange(onStateChange);

      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addAnimationState(AnimationType.IDLE, config);

      expect(onStateChange).toHaveBeenCalled();
      
      const event = onStateChange.mock.calls[0][0];
      expect(event.currentState.type).toBe(AnimationType.IDLE);
    });

    test('应该能够设置队列变化回调', () => {
      const onQueueChange = jest.fn();
      stateManager.setOnQueueChange(onQueueChange);

      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addToQueue(AnimationType.IDLE, config);

      expect(onQueueChange).toHaveBeenCalled();
      
      const queue = onQueueChange.mock.calls[0][0];
      expect(queue).toHaveLength(1);
      expect(queue[0].type).toBe(AnimationType.IDLE);
    });
  });

  describe('状态历史', () => {
    test('应该记录状态变化历史', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addAnimationState(AnimationType.IDLE, config);

      const history = stateManager.getStateHistory();
      expect(history).toHaveLength(1);
      expect(history[0].currentState.type).toBe(AnimationType.IDLE);
    });

    test('应该能够清空状态历史', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addAnimationState(AnimationType.IDLE, config);
      stateManager.clearStateHistory();

      const history = stateManager.getStateHistory();
      expect(history).toHaveLength(0);
    });

    test('应该限制历史记录数量', () => {
      // 添加超过最大历史数量的状态变化
      for (let i = 0; i < 60; i++) {
        const config: AnimationConfig = {
          type: AnimationType.IDLE,
          duration: 0.1
        };
        stateManager.addAnimationState(AnimationType.IDLE, config);
        stateManager.removeAnimationState(AnimationType.IDLE);
      }

      const history = stateManager.getStateHistory();
      expect(history.length).toBeLessThanOrEqual(50);
    });
  });

  describe('统计信息', () => {
    test('应该能够获取统计信息', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addAnimationState(AnimationType.IDLE, config);
      stateManager.addToQueue(AnimationType.TALKING, { type: AnimationType.TALKING });

      const stats = stateManager.getStatistics();

      expect(stats.currentStatesCount).toBe(1);
      expect(stats.queueLength).toBe(1);
      expect(stats.currentPriority).toBeGreaterThan(0);
      expect(stats.autoStateEnabled).toBe(true);
    });
  });

  describe('资源清理', () => {
    test('应该能够正确销毁状态管理器', () => {
      const config: AnimationConfig = {
        type: AnimationType.IDLE,
        duration: 2
      };

      stateManager.addAnimationState(AnimationType.IDLE, config);
      stateManager.addToQueue(AnimationType.TALKING, { type: AnimationType.TALKING });

      stateManager.dispose();

      expect(stateManager.getAllCurrentStates()).toHaveLength(0);
      expect(stateManager.getQueueStatus().length).toBe(0);
      expect(stateManager.getStateHistory()).toHaveLength(0);
    });
  });

  describe('错误处理', () => {
    test('应该能够处理无效的动画类型', () => {
      const config: AnimationConfig = {
        type: null as any,
        duration: 2
      };

      // 应该不会抛出错误
      expect(() => {
        stateManager.addAnimationState(null as any, config);
      }).not.toThrow();
    });

    test('应该能够处理空的配置', () => {
      // 应该不会抛出错误
      expect(() => {
        stateManager.addAnimationState(AnimationType.IDLE, null as any);
      }).not.toThrow();
    });
  });
});