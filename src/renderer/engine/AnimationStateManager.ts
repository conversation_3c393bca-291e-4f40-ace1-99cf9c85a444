import * as THREE from 'three';
import { AnimationType, AnimationState, AnimationConfig } from './AnimationSystem';

/**
 * 动画状态数据
 */
export interface AnimationStateData {
  type: AnimationType;
  state: AnimationState;
  startTime: number;
  duration: number;
  progress: number;
  weight: number;
  priority: number;
  isLooping: boolean;
}

/**
 * 动画队列项
 */
export interface AnimationQueueItem {
  type: AnimationType;
  config: AnimationConfig;
  delay?: number;
  callback?: () => void;
}

/**
 * 动画状态变化事件
 */
export interface AnimationStateChangeEvent {
  previousState: AnimationStateData | null;
  currentState: AnimationStateData;
  timestamp: number;
}

/**
 * 动画状态管理器
 * 负责管理动画的状态、队列和优先级控制
 */
export class AnimationStateManager {
  private currentStates: Map<AnimationType, AnimationStateData> = new Map();
  private animationQueue: AnimationQueueItem[] = [];
  private stateHistory: AnimationStateChangeEvent[] = [];
  private maxHistorySize: number = 50;
  
  // 优先级管理
  private priorityLevels: Map<AnimationType, number> = new Map();
  private currentPriority: number = 0;
  
  // 状态变化回调
  private onStateChange?: (event: AnimationStateChangeEvent) => void;
  private onQueueChange?: (queue: AnimationQueueItem[]) => void;
  
  // 自动状态管理
  private autoStateEnabled: boolean = true;
  private idleTimeout: number = 30000; // 30秒
  private lastActivityTime: number = Date.now();
  private stateCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializePriorityLevels();
    this.startStateMonitoring();
  }

  /**
   * 初始化优先级级别
   */
  private initializePriorityLevels(): void {
    this.priorityLevels.set(AnimationType.IDLE, 1);
    this.priorityLevels.set(AnimationType.THINKING, 2);
    this.priorityLevels.set(AnimationType.WAVING, 3);
    this.priorityLevels.set(AnimationType.HAPPY, 4);
    this.priorityLevels.set(AnimationType.SURPRISED, 5);
    this.priorityLevels.set(AnimationType.TALKING, 6);
    this.priorityLevels.set(AnimationType.WALKING, 3);
    this.priorityLevels.set(AnimationType.DANCING, 4);
  }

  /**
   * 开始状态监控
   */
  private startStateMonitoring(): void {
    if (this.stateCheckInterval) {
      clearInterval(this.stateCheckInterval);
    }

    this.stateCheckInterval = setInterval(() => {
      this.updateStates();
      this.processQueue();
      this.checkAutoState();
    }, 100); // 每100ms检查一次
  }

  /**
   * 添加动画状态
   */
  public addAnimationState(
    type: AnimationType,
    config: AnimationConfig,
    startTime: number = Date.now()
  ): boolean {
    const priority = this.priorityLevels.get(type) || 1;
    
    // 检查优先级
    if (!this.canPlayAnimation(type, priority)) {
      console.log(`动画优先级不足，添加到队列: ${type}`);
      this.addToQueue(type, config);
      return false;
    }

    const previousState = this.getCurrentHighestPriorityState();
    
    // 验证配置参数
    if (!config) {
      console.warn('动画配置为空，使用默认配置');
      config = { type };
    }

    const stateData: AnimationStateData = {
      type,
      state: AnimationState.PLAYING,
      startTime,
      duration: config.duration || 0,
      progress: 0,
      weight: config.weight || 1.0,
      priority,
      isLooping: config.loop || false
    };

    this.currentStates.set(type, stateData);
    this.currentPriority = Math.max(this.currentPriority, priority);
    this.lastActivityTime = Date.now();

    // 触发状态变化事件
    this.triggerStateChange(previousState, stateData);

    console.log(`动画状态已添加: ${type} (优先级: ${priority})`);
    return true;
  }

  /**
   * 移除动画状态
   */
  public removeAnimationState(type: AnimationType): boolean {
    const stateData = this.currentStates.get(type);
    if (!stateData) {
      return false;
    }

    const previousState = stateData;
    this.currentStates.delete(type);

    // 重新计算当前优先级
    this.recalculateCurrentPriority();

    // 如果移除的是当前最高优先级动画，尝试播放队列中的下一个
    if (stateData.priority === this.currentPriority) {
      this.processQueue();
    }

    console.log(`动画状态已移除: ${type}`);
    return true;
  }

  /**
   * 更新动画状态
   */
  public updateAnimationState(
    type: AnimationType,
    updates: Partial<AnimationStateData>
  ): boolean {
    const stateData = this.currentStates.get(type);
    if (!stateData) {
      return false;
    }

    const previousState = { ...stateData };
    Object.assign(stateData, updates);

    // 触发状态变化事件
    this.triggerStateChange(previousState, stateData);

    return true;
  }

  /**
   * 获取动画状态
   */
  public getAnimationState(type: AnimationType): AnimationStateData | null {
    return this.currentStates.get(type) || null;
  }

  /**
   * 获取所有当前状态
   */
  public getAllCurrentStates(): AnimationStateData[] {
    return Array.from(this.currentStates.values());
  }

  /**
   * 获取当前最高优先级状态
   */
  public getCurrentHighestPriorityState(): AnimationStateData | null {
    let highestPriorityState: AnimationStateData | null = null;
    let maxPriority = 0;

    for (const state of this.currentStates.values()) {
      if (state.priority > maxPriority) {
        maxPriority = state.priority;
        highestPriorityState = state;
      }
    }

    return highestPriorityState;
  }

  /**
   * 检查是否可以播放动画
   */
  public canPlayAnimation(type: AnimationType, priority?: number): boolean {
    const animationPriority = priority || this.priorityLevels.get(type) || 1;
    
    // 如果没有当前动画，可以播放
    if (this.currentStates.size === 0) {
      return true;
    }

    // 检查优先级 - 如果当前没有动画或新动画优先级更高，则可以播放
    return this.currentStates.size === 0 || animationPriority > this.currentPriority;
  }

  /**
   * 添加到动画队列
   */
  public addToQueue(
    type: AnimationType,
    config: AnimationConfig,
    delay?: number,
    callback?: () => void
  ): void {
    const queueItem: AnimationQueueItem = {
      type,
      config,
      delay,
      callback
    };

    // 按优先级插入队列
    const priority = this.priorityLevels.get(type) || 1;
    let insertIndex = this.animationQueue.length;

    for (let i = 0; i < this.animationQueue.length; i++) {
      const itemPriority = this.priorityLevels.get(this.animationQueue[i].type) || 1;
      if (priority > itemPriority) {
        insertIndex = i;
        break;
      }
    }

    this.animationQueue.splice(insertIndex, 0, queueItem);

    // 触发队列变化事件
    if (this.onQueueChange) {
      this.onQueueChange([...this.animationQueue]);
    }

    console.log(`动画已添加到队列: ${type} (位置: ${insertIndex})`);
  }

  /**
   * 处理动画队列
   */
  private processQueue(): void {
    if (this.animationQueue.length === 0) {
      return;
    }

    // 检查是否有可以播放的动画
    for (let i = 0; i < this.animationQueue.length; i++) {
      const queueItem = this.animationQueue[i];
      const priority = this.priorityLevels.get(queueItem.type) || 1;

      if (this.canPlayAnimation(queueItem.type, priority)) {
        // 从队列中移除并播放
        this.animationQueue.splice(i, 1);
        
        // 延迟播放
        if (queueItem.delay && queueItem.delay > 0) {
          const timeoutId = setTimeout(() => {
            this.addAnimationState(queueItem.type, queueItem.config);
            if (queueItem.callback) {
              queueItem.callback();
            }
          }, queueItem.delay);
          
          // 存储timeout ID以便可能的取消操作
          (queueItem as any).timeoutId = timeoutId;
        } else {
          this.addAnimationState(queueItem.type, queueItem.config);
          if (queueItem.callback) {
            queueItem.callback();
          }
        }

        // 触发队列变化事件
        if (this.onQueueChange) {
          this.onQueueChange([...this.animationQueue]);
        }

        break;
      }
    }
  }

  /**
   * 清空动画队列
   */
  public clearQueue(): void {
    this.animationQueue.length = 0;
    
    if (this.onQueueChange) {
      this.onQueueChange([]);
    }

    console.log('动画队列已清空');
  }

  /**
   * 获取队列状态
   */
  public getQueueStatus(): {
    length: number;
    items: AnimationQueueItem[];
    nextAnimation: AnimationType | null;
  } {
    return {
      length: this.animationQueue.length,
      items: [...this.animationQueue],
      nextAnimation: this.animationQueue.length > 0 ? this.animationQueue[0].type : null
    };
  }

  /**
   * 更新所有状态
   */
  private updateStates(): void {
    const now = Date.now();
    const statesToRemove: AnimationType[] = [];

    for (const [type, state] of this.currentStates) {
      // 更新进度
      if (state.duration > 0) {
        const elapsed = now - state.startTime;
        state.progress = Math.min(elapsed / (state.duration * 1000), 1.0);

        // 检查是否完成
        if (state.progress >= 1.0 && !state.isLooping) {
          statesToRemove.push(type);
        }
      }
    }

    // 移除已完成的动画
    statesToRemove.forEach(type => {
      this.removeAnimationState(type);
    });
  }

  /**
   * 检查自动状态
   */
  private checkAutoState(): void {
    if (!this.autoStateEnabled) {
      return;
    }

    const now = Date.now();
    const timeSinceLastActivity = now - this.lastActivityTime;

    // 如果超过空闲时间且没有当前动画，播放待机动画
    if (
      timeSinceLastActivity > this.idleTimeout &&
      this.currentStates.size === 0
    ) {
      this.addAnimationState(AnimationType.IDLE, {
        type: AnimationType.IDLE,
        loop: true,
        duration: 2
      });
    }
  }

  /**
   * 重新计算当前优先级
   */
  private recalculateCurrentPriority(): void {
    this.currentPriority = 0;
    for (const state of this.currentStates.values()) {
      this.currentPriority = Math.max(this.currentPriority, state.priority);
    }
  }

  /**
   * 触发状态变化事件
   */
  private triggerStateChange(
    previousState: AnimationStateData | null,
    currentState: AnimationStateData
  ): void {
    const event: AnimationStateChangeEvent = {
      previousState,
      currentState,
      timestamp: Date.now()
    };

    // 添加到历史记录
    this.stateHistory.push(event);
    if (this.stateHistory.length > this.maxHistorySize) {
      this.stateHistory.shift();
    }

    // 触发回调
    if (this.onStateChange) {
      this.onStateChange(event);
    }
  }

  /**
   * 设置优先级
   */
  public setPriority(type: AnimationType, priority: number): void {
    this.priorityLevels.set(type, priority);
    console.log(`动画优先级已设置: ${type} = ${priority}`);
  }

  /**
   * 获取优先级
   */
  public getPriority(type: AnimationType): number {
    return this.priorityLevels.get(type) || 1;
  }

  /**
   * 强制播放动画（忽略优先级）
   */
  public forcePlayAnimation(type: AnimationType, config: AnimationConfig): void {
    // 停止所有当前动画
    this.stopAllAnimations();
    
    // 播放指定动画
    this.addAnimationState(type, config);
    
    console.log(`强制播放动画: ${type}`);
  }

  /**
   * 停止所有动画
   */
  public stopAllAnimations(): void {
    const types = Array.from(this.currentStates.keys());
    types.forEach(type => {
      this.removeAnimationState(type);
    });
    
    this.clearQueue();
    console.log('所有动画已停止');
  }

  /**
   * 暂停所有动画
   */
  public pauseAllAnimations(): void {
    for (const state of this.currentStates.values()) {
      state.state = AnimationState.PAUSED;
    }
    console.log('所有动画已暂停');
  }

  /**
   * 恢复所有动画
   */
  public resumeAllAnimations(): void {
    for (const state of this.currentStates.values()) {
      if (state.state === AnimationState.PAUSED) {
        state.state = AnimationState.PLAYING;
      }
    }
    console.log('所有动画已恢复');
  }

  /**
   * 设置自动状态管理
   */
  public setAutoStateEnabled(enabled: boolean): void {
    this.autoStateEnabled = enabled;
    console.log(`自动状态管理: ${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 设置空闲超时时间
   */
  public setIdleTimeout(timeout: number): void {
    this.idleTimeout = timeout;
    console.log(`空闲超时时间设置为: ${timeout}ms`);
  }

  /**
   * 记录活动时间
   */
  public recordActivity(): void {
    this.lastActivityTime = Date.now();
  }

  /**
   * 设置状态变化回调
   */
  public setOnStateChange(callback: (event: AnimationStateChangeEvent) => void): void {
    this.onStateChange = callback;
  }

  /**
   * 设置队列变化回调
   */
  public setOnQueueChange(callback: (queue: AnimationQueueItem[]) => void): void {
    this.onQueueChange = callback;
  }

  /**
   * 获取状态历史
   */
  public getStateHistory(): AnimationStateChangeEvent[] {
    return [...this.stateHistory];
  }

  /**
   * 清空状态历史
   */
  public clearStateHistory(): void {
    this.stateHistory.length = 0;
    console.log('状态历史已清空');
  }

  /**
   * 获取统计信息
   */
  public getStatistics(): {
    currentStatesCount: number;
    queueLength: number;
    historyLength: number;
    currentPriority: number;
    lastActivityTime: number;
    autoStateEnabled: boolean;
  } {
    return {
      currentStatesCount: this.currentStates.size,
      queueLength: this.animationQueue.length,
      historyLength: this.stateHistory.length,
      currentPriority: this.currentPriority,
      lastActivityTime: this.lastActivityTime,
      autoStateEnabled: this.autoStateEnabled
    };
  }

  /**
   * 销毁状态管理器
   */
  public dispose(): void {
    // 清理定时器
    if (this.stateCheckInterval) {
      clearInterval(this.stateCheckInterval);
      this.stateCheckInterval = null;
    }

    // 清理所有状态
    this.stopAllAnimations();
    this.clearStateHistory();
    
    // 清理回调
    this.onStateChange = undefined;
    this.onQueueChange = undefined;

    console.log('动画状态管理器已销毁');
  }
}