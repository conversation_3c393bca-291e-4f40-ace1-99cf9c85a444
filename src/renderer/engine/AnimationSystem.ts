import * as THREE from 'three';

/**
 * 动画类型枚举
 */
export enum AnimationType {
  IDLE = 'idle',
  TALKING = 'talking',
  HAPPY = 'happy',
  SURPRISED = 'surprised',
  THINKING = 'thinking',
  WALKING = 'walking',
  WAVING = 'waving',
  DANCING = 'dancing'
}

/**
 * 动画状态枚举
 */
export enum AnimationState {
  STOPPED = 'stopped',
  PLAYING = 'playing',
  PAUSED = 'paused',
  TRANSITIONING = 'transitioning'
}

/**
 * 动画配置接口
 */
export interface AnimationConfig {
  type: AnimationType;
  duration?: number;
  loop?: boolean;
  fadeInTime?: number;
  fadeOutTime?: number;
  priority?: number;
  weight?: number;
}

/**
 * 动画数据接口
 */
export interface AnimationData {
  clip: THREE.AnimationClip;
  action: THREE.AnimationAction;
  config: AnimationConfig;
  mixer: THREE.AnimationMixer;
}

/**
 * 动画事件回调接口
 */
export interface AnimationCallbacks {
  onAnimationStart?: (type: AnimationType) => void;
  onAnimationEnd?: (type: AnimationType) => void;
  onAnimationLoop?: (type: AnimationType) => void;
  onTransitionStart?: (from: AnimationType, to: AnimationType) => void;
  onTransitionEnd?: (from: AnimationType, to: AnimationType) => void;
}

/**
 * 动画播放系统
 * 负责管理3D模型的动画播放、过渡和状态控制
 */
export class AnimationSystem {
  private mixer: THREE.AnimationMixer | null = null;
  private animations: Map<AnimationType, AnimationData> = new Map();
  private currentAnimation: AnimationType | null = null;
  private previousAnimation: AnimationType | null = null;
  private animationState: AnimationState = AnimationState.STOPPED;
  private clock: THREE.Clock = new THREE.Clock();
  
  // 待机动画定时器
  private idleTimer: NodeJS.Timeout | null = null;
  private idleDelay: number = 5000; // 5秒后播放待机动画（调试用）
  private lastInteractionTime: number = Date.now();
  
  // 动画过渡
  private transitionDuration: number = 0.5;
  private isTransitioning: boolean = false;
  
  // 事件回调
  private callbacks: AnimationCallbacks = {};
  
  // 动画权重管理
  private animationWeights: Map<AnimationType, number> = new Map();

  constructor() {
    this.setupDefaultAnimations();
    this.startIdleTimer();
  }

  /**
   * 初始化动画系统
   */
  public initialize(model: THREE.Group): void {
    console.log('🎬 开始初始化动画系统');
    console.log('🎬 模型信息:', {
      name: model.name,
      children: model.children.length,
      type: model.type
    });

    if (this.mixer) {
      this.dispose();
    }

    this.mixer = new THREE.AnimationMixer(model);
    console.log('✅ 动画混合器创建成功');

    this.setupMixerEvents();
    this.createBuiltInAnimations(model);

    console.log(`🎉 动画系统初始化完成，共创建 ${this.animations.size} 个动画`);

    // 立即播放待机动画
    setTimeout(() => {
      console.log('🎭 开始播放初始待机动画');
      this.playAnimation(AnimationType.IDLE);
    }, 100); // 稍微延迟一下确保所有初始化完成
  }

  /**
   * 设置动画混合器事件
   */
  private setupMixerEvents(): void {
    if (!this.mixer) return;

    this.mixer.addEventListener('finished', (event) => {
      const action = event.action as THREE.AnimationAction;
      const animationType = this.getAnimationTypeByAction(action);
      
      if (animationType && this.callbacks.onAnimationEnd) {
        this.callbacks.onAnimationEnd(animationType);
      }

      // 如果不是循环动画，回到待机状态
      if (animationType && !this.isLoopAnimation(animationType)) {
        this.playAnimation(AnimationType.IDLE);
      }
    });

    this.mixer.addEventListener('loop', (event) => {
      const action = event.action as THREE.AnimationAction;
      const animationType = this.getAnimationTypeByAction(action);
      
      if (animationType && this.callbacks.onAnimationLoop) {
        this.callbacks.onAnimationLoop(animationType);
      }
    });
  }

  /**
   * 创建内置动画
   */
  private createBuiltInAnimations(model: THREE.Group): void {
    if (!this.mixer) return;

    // 创建待机动画（呼吸效果）
    this.createIdleAnimation(model);
    
    // 创建说话动画
    this.createTalkingAnimation(model);
    
    // 创建开心动画
    this.createHappyAnimation(model);
    
    // 创建惊讶动画
    this.createSurprisedAnimation(model);
    
    // 创建思考动画
    this.createThinkingAnimation(model);
    
    // 创建挥手动画
    this.createWavingAnimation(model);

    console.log(`创建了 ${this.animations.size} 个内置动画`);
  }

  /**
   * 创建待机动画
   */
  private createIdleAnimation(model: THREE.Group): void {
    const times = [0, 1, 2];
    const positionValues = [
      0, 0, 0,  // 起始位置
      0, 0.05, 0,  // 上升
      0, 0, 0   // 回到起始位置
    ];
    const rotationValues = [
      0, 0, 0, 1,  // 起始旋转
      0, 0.05, 0, 1,  // 轻微摇摆
      0, 0, 0, 1   // 回到起始旋转
    ];

    const positionTrack = new THREE.VectorKeyframeTrack(
      '.position',
      times,
      positionValues
    );
    const rotationTrack = new THREE.QuaternionKeyframeTrack(
      '.quaternion',
      times,
      rotationValues
    );

    const clip = new THREE.AnimationClip('idle', 2, [positionTrack, rotationTrack]);
    const action = this.mixer!.clipAction(clip);
    action.setLoop(THREE.LoopRepeat, Infinity);

    this.animations.set(AnimationType.IDLE, {
      clip,
      action,
      config: {
        type: AnimationType.IDLE,
        duration: 2,
        loop: true,
        priority: 1,
        weight: 1.0
      },
      mixer: this.mixer!
    });
  }

  /**
   * 创建说话动画
   */
  private createTalkingAnimation(model: THREE.Group): void {
    const times = [0, 0.2, 0.4, 0.6, 0.8, 1.0];
    const scaleValues = [
      1, 1, 1,     // 正常大小
      1.02, 1.02, 1.02,  // 轻微放大
      1, 1, 1,     // 正常大小
      1.01, 1.01, 1.01,  // 轻微放大
      1, 1, 1,     // 正常大小
      1, 1, 1      // 正常大小
    ];

    const scaleTrack = new THREE.VectorKeyframeTrack(
      '.scale',
      times,
      scaleValues
    );

    const clip = new THREE.AnimationClip('talking', 1, [scaleTrack]);
    const action = this.mixer!.clipAction(clip);
    action.setLoop(THREE.LoopRepeat, Infinity);

    this.animations.set(AnimationType.TALKING, {
      clip,
      action,
      config: {
        type: AnimationType.TALKING,
        duration: 1,
        loop: true,
        priority: 5,
        weight: 1.0
      },
      mixer: this.mixer!
    });
  }

  /**
   * 创建开心动画
   */
  private createHappyAnimation(model: THREE.Group): void {
    const times = [0, 0.5, 1.0, 1.5, 2.0];
    const positionValues = [
      0, 0, 0,      // 起始位置
      0, 0.2, 0,    // 跳跃
      0, 0, 0,      // 落地
      0, 0.15, 0,   // 再次跳跃
      0, 0, 0       // 落地
    ];
    const rotationValues = [
      0, 0, 0, 1,           // 起始旋转
      0, 0.1, 0, 0.995,     // 轻微旋转
      0, 0, 0, 1,           // 回正
      0, -0.1, 0, 0.995,    // 反向旋转
      0, 0, 0, 1            // 回正
    ];

    const positionTrack = new THREE.VectorKeyframeTrack(
      '.position',
      times,
      positionValues
    );
    const rotationTrack = new THREE.QuaternionKeyframeTrack(
      '.quaternion',
      times,
      rotationValues
    );

    const clip = new THREE.AnimationClip('happy', 2, [positionTrack, rotationTrack]);
    const action = this.mixer!.clipAction(clip);
    action.setLoop(THREE.LoopOnce, 1);

    this.animations.set(AnimationType.HAPPY, {
      clip,
      action,
      config: {
        type: AnimationType.HAPPY,
        duration: 2,
        loop: false,
        priority: 3,
        weight: 1.0
      },
      mixer: this.mixer!
    });
  }

  /**
   * 创建惊讶动画
   */
  private createSurprisedAnimation(model: THREE.Group): void {
    const times = [0, 0.2, 0.4, 1.0];
    const scaleValues = [
      1, 1, 1,        // 正常大小
      1.1, 1.1, 1.1,  // 快速放大
      1.05, 1.05, 1.05, // 稍微缩小
      1, 1, 1         // 回到正常
    ];
    const positionValues = [
      0, 0, 0,    // 起始位置
      0, 0.1, 0,  // 轻微上升
      0, 0.05, 0, // 稍微下降
      0, 0, 0     // 回到起始位置
    ];

    const scaleTrack = new THREE.VectorKeyframeTrack(
      '.scale',
      times,
      scaleValues
    );
    const positionTrack = new THREE.VectorKeyframeTrack(
      '.position',
      times,
      positionValues
    );

    const clip = new THREE.AnimationClip('surprised', 1, [scaleTrack, positionTrack]);
    const action = this.mixer!.clipAction(clip);
    action.setLoop(THREE.LoopOnce, 1);

    this.animations.set(AnimationType.SURPRISED, {
      clip,
      action,
      config: {
        type: AnimationType.SURPRISED,
        duration: 1,
        loop: false,
        priority: 4,
        weight: 1.0
      },
      mixer: this.mixer!
    });
  }

  /**
   * 创建思考动画
   */
  private createThinkingAnimation(model: THREE.Group): void {
    const times = [0, 1, 2, 3, 4];
    const rotationValues = [
      0, 0, 0, 1,           // 起始旋转
      0, 0.05, 0, 0.999,    // 轻微左转
      0, 0, 0, 1,           // 回正
      0, -0.05, 0, 0.999,   // 轻微右转
      0, 0, 0, 1            // 回正
    ];

    const rotationTrack = new THREE.QuaternionKeyframeTrack(
      '.quaternion',
      times,
      rotationValues
    );

    const clip = new THREE.AnimationClip('thinking', 4, [rotationTrack]);
    const action = this.mixer!.clipAction(clip);
    action.setLoop(THREE.LoopRepeat, Infinity);

    this.animations.set(AnimationType.THINKING, {
      clip,
      action,
      config: {
        type: AnimationType.THINKING,
        duration: 4,
        loop: true,
        priority: 2,
        weight: 1.0
      },
      mixer: this.mixer!
    });
  }

  /**
   * 创建挥手动画
   */
  private createWavingAnimation(model: THREE.Group): void {
    // 这里创建一个简单的整体摇摆动画作为挥手的替代
    const times = [0, 0.5, 1.0, 1.5, 2.0];
    const rotationValues = [
      0, 0, 0, 1,           // 起始旋转
      0, 0.2, 0, 0.98,      // 向右摇摆
      0, 0, 0, 1,           // 回正
      0, 0.2, 0, 0.98,      // 再次向右摇摆
      0, 0, 0, 1            // 回正
    ];

    const rotationTrack = new THREE.QuaternionKeyframeTrack(
      '.quaternion',
      times,
      rotationValues
    );

    const clip = new THREE.AnimationClip('waving', 2, [rotationTrack]);
    const action = this.mixer!.clipAction(clip);
    action.setLoop(THREE.LoopOnce, 1);

    this.animations.set(AnimationType.WAVING, {
      clip,
      action,
      config: {
        type: AnimationType.WAVING,
        duration: 2,
        loop: false,
        priority: 3,
        weight: 1.0
      },
      mixer: this.mixer!
    });
  }

  /**
   * 加载VMD动画文件并添加到动画系统
   */
  public async loadVMDAnimation(
    vmdUrl: string,
    animationType: AnimationType,
    config?: Partial<AnimationConfig>
  ): Promise<boolean> {
    if (!this.mixer) {
      console.error('动画混合器未初始化');
      return false;
    }

    try {
      console.log('开始加载VMD动画:', vmdUrl, '类型:', animationType);
      
      // 这里应该使用VMDLoader加载实际的VMD文件
      // 由于VMD格式复杂，暂时创建一个模拟的VMD动画
      const vmdAnimation = await this.createMockVMDAnimation(animationType);
      
      if (vmdAnimation) {
        this.animations.set(animationType, vmdAnimation);
        console.log('VMD动画加载成功:', animationType);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('VMD动画加载失败:', error);
      return false;
    }
  }

  /**
   * 创建模拟的VMD动画（临时实现）
   */
  private async createMockVMDAnimation(animationType: AnimationType): Promise<AnimationData | null> {
    if (!this.mixer) return null;

    // 根据动画类型创建不同的VMD风格动画
    let clip: THREE.AnimationClip;
    let duration: number;
    let loop: boolean;
    let priority: number;

    switch (animationType) {
      case AnimationType.DANCING:
        clip = this.createDanceAnimation();
        duration = 8;
        loop = true;
        priority = 3;
        break;
      
      case AnimationType.WALKING:
        clip = this.createWalkAnimation();
        duration = 4;
        loop = true;
        priority = 2;
        break;
      
      default:
        // 创建一个通用的VMD风格动画
        clip = this.createGenericVMDAnimation(animationType);
        duration = 6;
        loop = false;
        priority = 3;
        break;
    }

    const action = this.mixer.clipAction(clip);
    action.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce, loop ? Infinity : 1);

    return {
      clip,
      action,
      config: {
        type: animationType,
        duration,
        loop,
        priority,
        weight: 1.0
      },
      mixer: this.mixer
    };
  }

  /**
   * 创建舞蹈动画
   */
  private createDanceAnimation(): THREE.AnimationClip {
    const times = [0, 1, 2, 3, 4, 5, 6, 7, 8];
    
    // 复杂的舞蹈动作 - 上下跳跃 + 旋转 + 摇摆
    const positionValues = [
      0, 0, 0,      // 起始
      0, 0.3, 0,    // 跳跃
      0, 0, 0,      // 落地
      0, 0.4, 0,    // 更高跳跃
      0, 0, 0,      // 落地
      0, 0.2, 0,    // 小跳
      0, 0, 0,      // 落地
      0, 0.5, 0,    // 最高跳跃
      0, 0, 0       // 结束
    ];

    const rotationValues = [
      0, 0, 0, 1,           // 起始
      0, 0.2, 0, 0.98,      // 右转
      0, 0, 0, 1,           // 回正
      0, -0.2, 0, 0.98,     // 左转
      0, 0, 0, 1,           // 回正
      0, 0.3, 0, 0.95,      // 大幅右转
      0, 0, 0, 1,           // 回正
      0, -0.3, 0, 0.95,     // 大幅左转
      0, 0, 0, 1            // 回正
    ];

    const scaleValues = [
      1, 1, 1,        // 正常
      1.1, 1.1, 1.1,  // 放大
      1, 1, 1,        // 正常
      1.15, 1.15, 1.15, // 更大
      1, 1, 1,        // 正常
      1.05, 1.05, 1.05, // 轻微放大
      1, 1, 1,        // 正常
      1.2, 1.2, 1.2,  // 最大
      1, 1, 1         // 回正
    ];

    const positionTrack = new THREE.VectorKeyframeTrack('.position', times, positionValues);
    const rotationTrack = new THREE.QuaternionKeyframeTrack('.quaternion', times, rotationValues);
    const scaleTrack = new THREE.VectorKeyframeTrack('.scale', times, scaleValues);

    return new THREE.AnimationClip('dance', 8, [positionTrack, rotationTrack, scaleTrack]);
  }

  /**
   * 创建走路动画
   */
  private createWalkAnimation(): THREE.AnimationClip {
    const times = [0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4];
    
    // 走路动作 - 前后移动 + 轻微上下浮动
    const positionValues = [
      0, 0, 0,        // 起始
      0, 0.05, 0.2,   // 向前一步，轻微抬起
      0, 0, 0.4,      // 向前，落地
      0, 0.05, 0.6,   // 继续向前，抬起
      0, 0, 0.8,      // 向前，落地
      0, 0.05, 1.0,   // 向前，抬起
      0, 0, 1.2,      // 向前，落地
      0, 0.05, 1.4,   // 向前，抬起
      0, 0, 1.6       // 最终位置
    ];

    const rotationValues = [
      0, 0, 0, 1,           // 起始
      0, 0.02, 0, 0.9998,   // 轻微摇摆
      0, 0, 0, 1,           // 回正
      0, -0.02, 0, 0.9998,  // 反向摇摆
      0, 0, 0, 1,           // 回正
      0, 0.02, 0, 0.9998,   // 摇摆
      0, 0, 0, 1,           // 回正
      0, -0.02, 0, 0.9998,  // 反向摇摆
      0, 0, 0, 1            // 回正
    ];

    const positionTrack = new THREE.VectorKeyframeTrack('.position', times, positionValues);
    const rotationTrack = new THREE.QuaternionKeyframeTrack('.quaternion', times, rotationValues);

    return new THREE.AnimationClip('walk', 4, [positionTrack, rotationTrack]);
  }

  /**
   * 创建通用VMD风格动画
   */
  private createGenericVMDAnimation(animationType: AnimationType): THREE.AnimationClip {
    const times = [0, 1, 2, 3, 4, 5, 6];
    
    // 通用的VMD风格动作 - 优雅的动作序列
    const positionValues = [
      0, 0, 0,      // 起始
      0, 0.1, 0,    // 轻微上升
      0, 0.2, 0,    // 继续上升
      0, 0.15, 0,   // 稍微下降
      0, 0.25, 0,   // 再次上升
      0, 0.1, 0,    // 下降
      0, 0, 0       // 回到起始
    ];

    const rotationValues = [
      0, 0, 0, 1,           // 起始
      0, 0.1, 0, 0.995,     // 轻微转动
      0, 0.15, 0, 0.989,    // 继续转动
      0, 0.05, 0, 0.999,    // 回转
      0, -0.1, 0, 0.995,    // 反向转动
      0, -0.05, 0, 0.999,   // 轻微回转
      0, 0, 0, 1            // 回正
    ];

    const scaleValues = [
      1, 1, 1,        // 正常
      1.02, 1.02, 1.02, // 轻微放大
      1.05, 1.05, 1.05, // 继续放大
      1.03, 1.03, 1.03, // 稍微缩小
      1.06, 1.06, 1.06, // 再次放大
      1.02, 1.02, 1.02, // 缩小
      1, 1, 1         // 回正
    ];

    const positionTrack = new THREE.VectorKeyframeTrack('.position', times, positionValues);
    const rotationTrack = new THREE.QuaternionKeyframeTrack('.quaternion', times, rotationValues);
    const scaleTrack = new THREE.VectorKeyframeTrack('.scale', times, scaleValues);

    return new THREE.AnimationClip(`vmd_${animationType}`, 6, [positionTrack, rotationTrack, scaleTrack]);
  }

  /**
   * 播放动画
   */
  public playAnimation(
    type: AnimationType,
    config?: Partial<AnimationConfig>
  ): boolean {
    console.log(`🎭 尝试播放动画: ${type}`);
    console.log(`🎭 动画系统状态: mixer=${!!this.mixer}, animations=${this.animations.size}`);

    const animationData = this.animations.get(type);
    if (!animationData || !this.mixer) {
      console.warn(`❌ 动画不存在或混合器未初始化: ${type}, mixer=${!!this.mixer}, animationData=${!!animationData}`);
      return false;
    }

    console.log(`✅ 找到动画数据: ${type}`);

    // 更新最后交互时间
    this.lastInteractionTime = Date.now();

    // 如果已经在播放相同动画，直接返回
    if (this.currentAnimation === type && this.animationState === AnimationState.PLAYING) {
      console.log(`⏭️ 动画已在播放: ${type}`);
      return true;
    }

    console.log(`🔄 开始动画过渡: ${this.currentAnimation} -> ${type}`);

    // 开始过渡
    this.startTransition(type, config);

    return true;
  }

  /**
   * 开始动画过渡
   */
  private startTransition(
    targetType: AnimationType,
    config?: Partial<AnimationConfig>
  ): void {
    console.log(`🔄 开始过渡到动画: ${targetType}`);

    const targetAnimation = this.animations.get(targetType);
    if (!targetAnimation) {
      console.error(`❌ 目标动画不存在: ${targetType}`);
      return;
    }

    console.log(`✅ 找到目标动画: ${targetType}`);

    this.isTransitioning = true;
    this.animationState = AnimationState.TRANSITIONING;
    this.previousAnimation = this.currentAnimation;

    // 触发过渡开始回调
    if (this.callbacks.onTransitionStart && this.previousAnimation) {
      this.callbacks.onTransitionStart(this.previousAnimation, targetType);
    }

    // 停止当前动画
    if (this.currentAnimation) {
      console.log(`⏹️ 停止当前动画: ${this.currentAnimation}`);
      const currentAnimationData = this.animations.get(this.currentAnimation);
      if (currentAnimationData) {
        currentAnimationData.action.fadeOut(this.transitionDuration);
      }
    }

    // 应用配置
    if (config) {
      Object.assign(targetAnimation.config, config);
    }

    // 开始新动画
    console.log(`▶️ 开始播放动画: ${targetType}`);
    targetAnimation.action.reset();
    targetAnimation.action.fadeIn(this.transitionDuration);
    targetAnimation.action.play();

    // 设置权重
    const weight = targetAnimation.config.weight || 1.0;
    targetAnimation.action.setEffectiveWeight(weight);
    console.log(`⚖️ 设置动画权重: ${weight}`);

    this.currentAnimation = targetType;

    // 过渡完成后的处理
    setTimeout(() => {
      console.log(`✅ 动画过渡完成: ${targetType}`);
      this.isTransitioning = false;
      this.animationState = AnimationState.PLAYING;

      // 触发过渡结束回调
      if (this.callbacks.onTransitionEnd && this.previousAnimation) {
        this.callbacks.onTransitionEnd(this.previousAnimation, targetType);
      }

      // 触发动画开始回调
      if (this.callbacks.onAnimationStart) {
        this.callbacks.onAnimationStart(targetType);
      }
    }, this.transitionDuration * 1000);
  }

  /**
   * 停止动画
   */
  public stopAnimation(type?: AnimationType): void {
    if (type) {
      const animationData = this.animations.get(type);
      if (animationData) {
        animationData.action.stop();
      }
    } else {
      // 停止所有动画
      this.animations.forEach((animationData) => {
        animationData.action.stop();
      });
      this.currentAnimation = null;
      this.animationState = AnimationState.STOPPED;
    }
  }

  /**
   * 暂停动画
   */
  public pauseAnimation(): void {
    if (this.mixer) {
      this.mixer.timeScale = 0;
      this.animationState = AnimationState.PAUSED;
    }
  }

  /**
   * 恢复动画
   */
  public resumeAnimation(): void {
    if (this.mixer) {
      this.mixer.timeScale = 1;
      this.animationState = AnimationState.PLAYING;
    }
  }

  /**
   * 更新动画系统
   */
  public update(): void {
    if (this.mixer && this.animationState !== AnimationState.PAUSED) {
      const delta = this.clock.getDelta();
      this.mixer.update(delta);

      // 每隔一段时间输出调试信息
      if (Math.random() < 0.001) { // 大约每1000帧输出一次
        console.log(`🔄 动画系统更新: delta=${delta.toFixed(4)}, state=${this.animationState}, current=${this.currentAnimation}`);
      }
    }

    // 检查是否需要播放待机动画
    this.checkIdleAnimation();
  }

  /**
   * 检查待机动画
   */
  private checkIdleAnimation(): void {
    const now = Date.now();
    const timeSinceLastInteraction = now - this.lastInteractionTime;

    // 如果超过待机时间且当前不在播放动画，播放待机动画
    if (
      timeSinceLastInteraction > this.idleDelay &&
      (this.currentAnimation === null || this.currentAnimation === AnimationType.IDLE) &&
      !this.isTransitioning
    ) {
      if (this.currentAnimation !== AnimationType.IDLE) {
        this.playAnimation(AnimationType.IDLE);
      }
    }
  }

  /**
   * 设置待机延迟时间
   */
  public setIdleDelay(delay: number): void {
    this.idleDelay = delay;
  }

  /**
   * 设置过渡持续时间
   */
  public setTransitionDuration(duration: number): void {
    this.transitionDuration = duration;
  }

  /**
   * 设置事件回调
   */
  public setCallbacks(callbacks: AnimationCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 获取当前动画类型
   */
  public getCurrentAnimation(): AnimationType | null {
    return this.currentAnimation;
  }

  /**
   * 获取动画状态
   */
  public getAnimationState(): AnimationState {
    return this.animationState;
  }

  /**
   * 获取可用动画列表
   */
  public getAvailableAnimations(): AnimationType[] {
    return Array.from(this.animations.keys());
  }

  /**
   * 检查动画是否存在
   */
  public hasAnimation(type: AnimationType): boolean {
    return this.animations.has(type);
  }

  /**
   * 设置默认动画配置
   */
  private setupDefaultAnimations(): void {
    // 设置默认动画权重
    this.animationWeights.set(AnimationType.IDLE, 1.0);
    this.animationWeights.set(AnimationType.TALKING, 1.0);
    this.animationWeights.set(AnimationType.HAPPY, 1.0);
    this.animationWeights.set(AnimationType.SURPRISED, 1.0);
    this.animationWeights.set(AnimationType.THINKING, 1.0);
    this.animationWeights.set(AnimationType.WAVING, 1.0);
  }

  /**
   * 启动待机定时器
   */
  private startIdleTimer(): void {
    if (this.idleTimer) {
      clearInterval(this.idleTimer);
    }

    this.idleTimer = setInterval(() => {
      this.checkIdleAnimation();
    }, 1000); // 每秒检查一次
  }

  /**
   * 根据动作获取动画类型
   */
  private getAnimationTypeByAction(action: THREE.AnimationAction): AnimationType | null {
    for (const [type, data] of this.animations) {
      if (data.action === action) {
        return type;
      }
    }
    return null;
  }

  /**
   * 检查是否为循环动画
   */
  private isLoopAnimation(type: AnimationType): boolean {
    const animationData = this.animations.get(type);
    return animationData?.config.loop || false;
  }

  /**
   * 销毁动画系统
   */
  public dispose(): void {
    // 清理定时器
    if (this.idleTimer) {
      clearInterval(this.idleTimer);
      this.idleTimer = null;
    }

    // 停止所有动画
    this.stopAnimation();

    // 清理动画数据
    this.animations.forEach((animationData) => {
      animationData.action.stop();
    });
    this.animations.clear();

    // 清理混合器
    if (this.mixer) {
      this.mixer.stopAllAction();
      this.mixer = null;
    }

    // 重置状态
    this.currentAnimation = null;
    this.previousAnimation = null;
    this.animationState = AnimationState.STOPPED;
    this.isTransitioning = false;
    this.callbacks = {};
    this.animationWeights.clear();

    console.log('动画系统已销毁');
  }
}