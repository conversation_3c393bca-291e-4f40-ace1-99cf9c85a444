import { renderHook, act } from '@testing-library/react';
import { useWindowDrag } from '../useWindowDrag';

// Mock window.electronAPI
const mockElectronAPI = {
  startDrag: jest.fn(),
  stopDrag: jest.fn(),
  dragMove: jest.fn(),
};

Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

// Mock MouseEvent
class MockMouseEvent {
  button: number;
  clientX: number;
  clientY: number;
  screenX: number;
  screenY: number;
  currentTarget: HTMLElement;

  constructor(type: string, options: any = {}) {
    this.button = options.button || 0;
    this.clientX = options.clientX || 0;
    this.clientY = options.clientY || 0;
    this.screenX = options.screenX || 0;
    this.screenY = options.screenY || 0;
    this.currentTarget = options.currentTarget || {
      getBoundingClientRect: () => ({ left: 0, top: 0 })
    } as HTMLElement;
  }

  preventDefault() {}
}

describe('useWindowDrag', () => {
  let addEventListenerSpy: jest.SpyInstance;
  let removeEventListenerSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.clearAllMocks();
    addEventListenerSpy = jest.spyOn(document, 'addEventListener');
    removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
  });

  afterEach(() => {
    addEventListenerSpy.mockRestore();
    removeEventListenerSpy.mockRestore();
  });

  it('should setup event listeners on mount', () => {
    renderHook(() => useWindowDrag());

    expect(addEventListenerSpy).toHaveBeenCalledWith('mousemove', expect.any(Function));
    expect(addEventListenerSpy).toHaveBeenCalledWith('mouseup', expect.any(Function));
  });

  it('should cleanup event listeners on unmount', () => {
    const { unmount } = renderHook(() => useWindowDrag());

    unmount();

    expect(removeEventListenerSpy).toHaveBeenCalledWith('mousemove', expect.any(Function));
    expect(removeEventListenerSpy).toHaveBeenCalledWith('mouseup', expect.any(Function));
  });

  it('should handle mouse down event', () => {
    const { result } = renderHook(() => useWindowDrag());

    const mockEvent = new MockMouseEvent('mousedown', {
      button: 0,
      clientX: 50,
      clientY: 75,
      screenX: 100,
      screenY: 125,
      currentTarget: {
        getBoundingClientRect: () => ({ left: 10, top: 15 })
      }
    }) as any;

    act(() => {
      result.current.handleMouseDown(mockEvent);
    });

    expect(mockElectronAPI.startDrag).toHaveBeenCalledWith(40, 60); // 50-10, 75-15
  });

  it('should ignore non-left mouse button clicks', () => {
    const { result } = renderHook(() => useWindowDrag());

    const mockEvent = new MockMouseEvent('mousedown', {
      button: 1, // Right click
      clientX: 50,
      clientY: 75,
    }) as any;

    act(() => {
      result.current.handleMouseDown(mockEvent);
    });

    expect(mockElectronAPI.startDrag).not.toHaveBeenCalled();
  });

  it('should handle mouse move during drag', () => {
    const { result } = renderHook(() => useWindowDrag());

    // Start drag
    const mouseDownEvent = new MockMouseEvent('mousedown', {
      button: 0,
      clientX: 50,
      clientY: 75,
      screenX: 100,
      screenY: 125,
    }) as any;

    act(() => {
      result.current.handleMouseDown(mouseDownEvent);
    });

    // Simulate mouse move
    const mouseMoveEvent = new MouseEvent('mousemove', {
      screenX: 150,
      screenY: 175,
    });

    act(() => {
      document.dispatchEvent(mouseMoveEvent);
    });

    expect(mockElectronAPI.dragMove).toHaveBeenCalledWith(150, 175);
  });

  it('should handle mouse up to stop drag', () => {
    const { result } = renderHook(() => useWindowDrag());

    // Start drag
    const mouseDownEvent = new MockMouseEvent('mousedown', {
      button: 0,
      clientX: 50,
      clientY: 75,
    }) as any;

    act(() => {
      result.current.handleMouseDown(mouseDownEvent);
    });

    // Simulate mouse up
    const mouseUpEvent = new MouseEvent('mouseup');

    act(() => {
      document.dispatchEvent(mouseUpEvent);
    });

    expect(mockElectronAPI.stopDrag).toHaveBeenCalled();
  });

  it('should not handle mouse move when not dragging', () => {
    renderHook(() => useWindowDrag());

    // Simulate mouse move without starting drag
    const mouseMoveEvent = new MouseEvent('mousemove', {
      screenX: 150,
      screenY: 175,
    });

    act(() => {
      document.dispatchEvent(mouseMoveEvent);
    });

    expect(mockElectronAPI.dragMove).not.toHaveBeenCalled();
  });

  it('should stop drag on component unmount if dragging', () => {
    const { result, unmount } = renderHook(() => useWindowDrag());

    // Start drag
    const mouseDownEvent = new MockMouseEvent('mousedown', {
      button: 0,
      clientX: 50,
      clientY: 75,
    }) as any;

    act(() => {
      result.current.handleMouseDown(mouseDownEvent);
    });

    // Unmount component while dragging
    unmount();

    expect(mockElectronAPI.stopDrag).toHaveBeenCalled();
  });
});