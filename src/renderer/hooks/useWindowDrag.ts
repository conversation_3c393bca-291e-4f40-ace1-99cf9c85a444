import { useCallback, useRef, useEffect } from 'react';

interface DragState {
  isDragging: boolean;
  startX: number;
  startY: number;
  offsetX: number;
  offsetY: number;
}

export const useWindowDrag = () => {
  const dragStateRef = useRef<DragState>({
    isDragging: false,
    startX: 0,
    startY: 0,
    offsetX: 0,
    offsetY: 0,
  });

  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    // 只处理左键点击
    if (event.button !== 0) return;

    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const offsetX = event.clientX - rect.left;
    const offsetY = event.clientY - rect.top;

    dragStateRef.current = {
      isDragging: true,
      startX: event.screenX,
      startY: event.screenY,
      offsetX,
      offsetY,
    };

    // 通知主进程开始拖拽
    window.electronAPI?.startDrag(offsetX, offsetY);

    // 阻止默认行为
    event.preventDefault();
  }, []);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!dragStateRef.current.isDragging) return;

    // 通知主进程移动窗口
    window.electronAPI?.dragMove(event.screenX, event.screenY);
  }, []);

  const handleMouseUp = useCallback(() => {
    if (!dragStateRef.current.isDragging) return;

    dragStateRef.current.isDragging = false;
    
    // 通知主进程停止拖拽
    window.electronAPI?.stopDrag();
  }, []);

  // 设置全局鼠标事件监听
  useEffect(() => {
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  // 组件卸载时停止拖拽
  useEffect(() => {
    return () => {
      if (dragStateRef.current.isDragging) {
        window.electronAPI?.stopDrag();
      }
    };
  }, []);

  return {
    handleMouseDown,
    isDragging: dragStateRef.current.isDragging,
  };
};