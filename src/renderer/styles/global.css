/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: transparent;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.app {
  width: 100vw;
  height: 100vh;
  position: relative;
  background: transparent;
  user-select: none; /* 防止拖拽时选中文本 */
}

/* CSS拖拽区域 - 使用Electron内置拖拽 */
.drag-area-css {
  -webkit-app-region: drag;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 10px; /* 只覆盖顶部边缘 */
  z-index: 10000;
  background: transparent;
  pointer-events: auto;
}

/* 手动拖拽区域 - 用于高级控制 */
.drag-area-manual {
  position: fixed;
  top: 10px; /* 调整位置 */
  left: 0;
  right: 0;
  height: 10px; /* 减少高度 */
  z-index: 9999;
  cursor: move;
  background: rgba(255, 255, 255, 0.02); /* 几乎透明 */
  -webkit-app-region: no-drag;
  pointer-events: auto;
}

.drag-area-manual:hover {
  background: rgba(255, 255, 255, 0.1);
}

.drag-area-manual:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 确保其他交互元素不会被拖拽影响 */
.context-menu,
.loading-indicator,
button,
input,
select {
  -webkit-app-region: no-drag;
}

/* 退出按钮样式 */
.exit-button {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 0, 0, 0.7);
  color: white;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  -webkit-app-region: no-drag;
}

.exit-button:hover {
  background: rgba(255, 0, 0, 0.9);
  transform: scale(1.1);
}

.exit-button:active {
  transform: scale(0.95);
}

/* 加载指示器样式 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: rgba(30, 30, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 150px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.context-menu-item {
  padding: 8px 16px;
  color: #fff;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.context-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.context-menu-separator {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 4px 0;
}

/* 3D画布容器 */
.keqing-model-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 20px; /* 最小化顶部空间，让模型完整显示 */
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-app-region: no-drag;
  pointer-events: auto;
}

.keqing-model-container canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
  -webkit-app-region: no-drag;
}