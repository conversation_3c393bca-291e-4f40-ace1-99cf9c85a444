import React, { useEffect, useRef, useState } from 'react';
import { AnimationSystem, AnimationType, AnimationState } from '../engine/AnimationSystem';
import * as THREE from 'three';

interface AnimationDebugProps {
  animationSystem: AnimationSystem | null;
}

export const AnimationDebug: React.FC<AnimationDebugProps> = ({ animationSystem }) => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [logs, setLogs] = useState<string[]>([]);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!animationSystem) return;

    // 定期更新调试信息
    intervalRef.current = setInterval(() => {
      if (animationSystem) {
        try {
          setDebugInfo({
            currentAnimation: animationSystem.getCurrentAnimation(),
            animationState: animationSystem.getAnimationState(),
            availableAnimations: animationSystem.getAvailableAnimations(),
            isPlaying: animationSystem.getAnimationState() === AnimationState.PLAYING,
            timestamp: new Date().toLocaleTimeString()
          });
        } catch (error) {
          console.error('调试信息更新失败:', error);
        }
      }
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [animationSystem]);

  const handlePlayAnimation = (type: AnimationType) => {
    if (animationSystem) {
      console.log(`🎮 手动播放动画: ${type}`);
      animationSystem.playAnimation(type);
    }
  };

  const handleStopAnimation = () => {
    if (animationSystem) {
      console.log('🛑 手动停止动画');
      animationSystem.stopAnimation();
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      width: 300,
      maxHeight: 500,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: 10,
      borderRadius: 5,
      fontSize: 12,
      fontFamily: 'monospace',
      zIndex: 1000,
      overflow: 'auto'
    }}>
      <h3>动画系统调试</h3>
      
      <div style={{ marginBottom: 10 }}>
        <strong>状态信息:</strong>
        <div>当前动画: {debugInfo.currentAnimation || 'None'}</div>
        <div>动画状态: {debugInfo.animationState || 'Unknown'}</div>
        <div>可用动画: {debugInfo.availableAnimations?.length || 0}</div>
        <div>更新时间: {debugInfo.timestamp}</div>
      </div>

      <div style={{ marginBottom: 10 }}>
        <strong>控制按钮:</strong>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 5, marginTop: 5 }}>
          {Object.values(AnimationType).map(type => (
            <button
              key={type}
              onClick={() => handlePlayAnimation(type)}
              style={{
                padding: '2px 6px',
                fontSize: 10,
                backgroundColor: debugInfo.currentAnimation === type ? '#4CAF50' : '#333',
                color: 'white',
                border: 'none',
                borderRadius: 3,
                cursor: 'pointer'
              }}
            >
              {type}
            </button>
          ))}
          <button
            onClick={handleStopAnimation}
            style={{
              padding: '2px 6px',
              fontSize: 10,
              backgroundColor: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: 3,
              cursor: 'pointer'
            }}
          >
            停止
          </button>
        </div>
      </div>

      <div>
        <strong>说明:</strong>
        <div style={{ fontSize: 10, marginTop: 5 }}>
          点击按钮测试动画播放。如果动画不工作，请检查控制台输出。
        </div>
      </div>
    </div>
  );
};
