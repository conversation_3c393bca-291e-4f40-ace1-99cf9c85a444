import { BrowserWindow, screen, ipcMain } from 'electron';
import * as path from 'path';

export interface WindowConfig {
  width: number;
  height: number;
  x?: number;
  y?: number;
  transparent: boolean;
  alwaysOnTop: boolean;
  frame: boolean;
  resizable: boolean;
  skipTaskbar: boolean;
  clickThrough?: boolean;
}

export class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private isDragging = false;
  private dragOffset = { x: 0, y: 0 };

  constructor() {
    this.setupIpcHandlers();
  }

  /**
   * 创建主窗口
   */
  public createMainWindow(config?: Partial<WindowConfig>): BrowserWindow {
    const defaultConfig: WindowConfig = {
      width: 300,
      height: 400,
      transparent: true,
      alwaysOnTop: true,
      frame: false,
      resizable: false,
      skipTaskbar: true,
      clickThrough: false,
    };

    const windowConfig = { ...defaultConfig, ...config };
    
    // 获取屏幕尺寸并设置默认位置
    const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize;
    const defaultX = screenWidth - windowConfig.width - 100;
    const defaultY = screenHeight - windowConfig.height - 100;

    this.mainWindow = new BrowserWindow({
      width: windowConfig.width,
      height: windowConfig.height,
      x: windowConfig.x ?? defaultX,
      y: windowConfig.y ?? defaultY,
      frame: windowConfig.frame,
      transparent: windowConfig.transparent,
      alwaysOnTop: windowConfig.alwaysOnTop,
      resizable: windowConfig.resizable,
      skipTaskbar: windowConfig.skipTaskbar,
      show: false, // 初始不显示，等待内容加载完成
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
      },
    });

    // 设置点击穿透（如果需要）
    if (windowConfig.clickThrough) {
      this.setClickThrough(true);
    }

    // 窗口准备显示时才显示
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    // 设置窗口事件监听
    this.setupWindowEvents();

    return this.mainWindow;
  }

  /**
   * 获取主窗口实例
   */
  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  /**
   * 设置窗口点击穿透
   */
  public setClickThrough(enable: boolean): void {
    if (!this.mainWindow) return;

    if (enable) {
      this.mainWindow.setIgnoreMouseEvents(true, { forward: true });
    } else {
      this.mainWindow.setIgnoreMouseEvents(false);
    }
  }

  /**
   * 设置窗口始终置顶
   */
  public setAlwaysOnTop(enable: boolean): void {
    if (!this.mainWindow) return;
    this.mainWindow.setAlwaysOnTop(enable);
  }

  /**
   * 移动窗口到指定位置
   */
  public moveWindow(x: number, y: number): void {
    if (!this.mainWindow) return;
    
    // 确保窗口不会移出屏幕边界
    const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize;
    const [windowWidth, windowHeight] = this.mainWindow.getSize();
    
    const clampedX = Math.max(0, Math.min(x, screenWidth - windowWidth));
    const clampedY = Math.max(0, Math.min(y, screenHeight - windowHeight));
    
    this.mainWindow.setPosition(clampedX, clampedY);
  }

  /**
   * 开始拖拽窗口
   */
  public startDrag(offsetX: number, offsetY: number): void {
    this.isDragging = true;
    this.dragOffset = { x: offsetX, y: offsetY };
  }

  /**
   * 停止拖拽窗口
   */
  public stopDrag(): void {
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
  }

  /**
   * 处理拖拽移动
   */
  public handleDragMove(mouseX: number, mouseY: number): void {
    if (!this.isDragging || !this.mainWindow) return;
    
    const newX = mouseX - this.dragOffset.x;
    const newY = mouseY - this.dragOffset.y;
    
    this.moveWindow(newX, newY);
  }

  /**
   * 显示窗口
   */
  public showWindow(): void {
    if (!this.mainWindow) return;
    this.mainWindow.show();
  }

  /**
   * 隐藏窗口
   */
  public hideWindow(): void {
    if (!this.mainWindow) return;
    this.mainWindow.hide();
  }

  /**
   * 关闭窗口
   */
  public closeWindow(): void {
    if (!this.mainWindow) return;
    this.mainWindow.close();
    this.mainWindow = null;
  }

  /**
   * 获取窗口位置和尺寸
   */
  public getWindowBounds(): { x: number; y: number; width: number; height: number } | null {
    if (!this.mainWindow) return null;
    return this.mainWindow.getBounds();
  }

  /**
   * 设置窗口事件监听
   */
  private setupWindowEvents(): void {
    if (!this.mainWindow) return;

    // 窗口关闭事件
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // 窗口移动事件
    this.mainWindow.on('moved', () => {
      // 可以在这里保存窗口位置到配置文件
      const bounds = this.getWindowBounds();
      if (bounds) {
        // TODO: 保存位置到配置文件
        console.log('Window moved to:', bounds.x, bounds.y);
      }
    });

    // 窗口失去焦点时停止拖拽
    this.mainWindow.on('blur', () => {
      this.stopDrag();
    });
  }

  /**
   * 设置IPC处理器
   */
  private setupIpcHandlers(): void {
    // 处理来自渲染进程的窗口控制请求
    ipcMain.handle('window-start-drag', (_, offsetX: number, offsetY: number) => {
      this.startDrag(offsetX, offsetY);
    });

    ipcMain.handle('window-stop-drag', () => {
      this.stopDrag();
    });

    ipcMain.handle('window-drag-move', (_, mouseX: number, mouseY: number) => {
      this.handleDragMove(mouseX, mouseY);
    });

    ipcMain.handle('window-set-click-through', (_, enable: boolean) => {
      this.setClickThrough(enable);
    });

    ipcMain.handle('window-set-always-on-top', (_, enable: boolean) => {
      this.setAlwaysOnTop(enable);
    });

    ipcMain.handle('window-show', () => {
      this.showWindow();
    });

    ipcMain.handle('window-hide', () => {
      this.hideWindow();
    });

    ipcMain.handle('window-get-bounds', () => {
      return this.getWindowBounds();
    });

    // 窗口控制处理器
    ipcMain.handle('window:minimize', () => {
      if (this.mainWindow) {
        this.mainWindow.minimize();
      }
    });

    ipcMain.handle('window:close', () => {
      this.closeWindow();
    });

    // 应用退出处理器
    ipcMain.handle('app:quit', () => {
      const { app } = require('electron');
      app.quit();
    });
  }
}