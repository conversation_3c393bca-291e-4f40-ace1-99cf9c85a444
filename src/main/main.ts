import { app, BrowserWindow } from 'electron';
import * as path from 'path';
import { WindowManager } from './WindowManager';

class KeqingDesktopPet {
  private windowManager: WindowManager;

  constructor() {
    this.windowManager = new WindowManager();
    this.initializeApp();
  }

  private initializeApp(): void {
    // 当Electron完成初始化时创建窗口
    app.whenReady().then(() => {
      this.createWindow();

      // macOS特定：当dock图标被点击且没有其他窗口打开时重新创建窗口
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createWindow();
        }
      });
    });

    // 当所有窗口关闭时退出应用（除了macOS）
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });
  }

  private createWindow(): void {
    // 使用WindowManager创建窗口
    const mainWindow = this.windowManager.createMainWindow({
      width: 300,
      height: 400,
    });

    // 加载应用
    if (process.env.NODE_ENV === 'development') {
      mainWindow.loadURL('http://localhost:3000');
      mainWindow.webContents.openDevTools();
    } else {
      mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
      // 临时开启开发者工具用于调试
      mainWindow.webContents.openDevTools();
    }
  }
}

// 创建应用实例
new KeqingDesktopPet();