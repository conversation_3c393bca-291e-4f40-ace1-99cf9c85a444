import { contextBridge, ipcRenderer } from 'electron';

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 窗口控制
  minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
  closeWindow: () => ipcRenderer.invoke('window:close'),
  quitApp: () => ipcRenderer.invoke('app:quit'),
  
  // 窗口管理
  startDrag: (offsetX: number, offsetY: number) => ipcRenderer.invoke('window-start-drag', offsetX, offsetY),
  stopDrag: () => ipcRenderer.invoke('window-stop-drag'),
  dragMove: (mouseX: number, mouseY: number) => ipcRenderer.invoke('window-drag-move', mouseX, mouseY),
  setClickThrough: (enable: boolean) => ipcRenderer.invoke('window-set-click-through', enable),
  setAlwaysOnTop: (enable: boolean) => ipcRenderer.invoke('window-set-always-on-top', enable),
  showWindow: () => ipcRenderer.invoke('window-show'),
  hideWindow: () => ipcRenderer.invoke('window-hide'),
  getWindowBounds: () => ipcRenderer.invoke('window-get-bounds'),
  
  // 系统托盘
  showTrayMenu: () => ipcRenderer.invoke('tray:show-menu'),
  
  // 设置管理
  getSettings: () => ipcRenderer.invoke('settings:get'),
  saveSettings: (settings: any) => ipcRenderer.invoke('settings:save', settings),
  
  // 文件操作
  selectModelFile: () => ipcRenderer.invoke('file:select-model'),
  selectAudioFile: () => ipcRenderer.invoke('file:select-audio'),
  
  // 事件监听
  onSettingsChanged: (callback: (settings: any) => void) => {
    ipcRenderer.on('settings:changed', (_, settings) => callback(settings));
  },
  
  // 移除监听器
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
});

// 类型定义
declare global {
  interface Window {
    electronAPI: {
      minimizeWindow: () => Promise<void>;
      closeWindow: () => Promise<void>;
      quitApp: () => Promise<void>;
      
      // 窗口管理
      startDrag: (offsetX: number, offsetY: number) => Promise<void>;
      stopDrag: () => Promise<void>;
      dragMove: (mouseX: number, mouseY: number) => Promise<void>;
      setClickThrough: (enable: boolean) => Promise<void>;
      setAlwaysOnTop: (enable: boolean) => Promise<void>;
      showWindow: () => Promise<void>;
      hideWindow: () => Promise<void>;
      getWindowBounds: () => Promise<{ x: number; y: number; width: number; height: number } | null>;
      
      showTrayMenu: () => Promise<void>;
      getSettings: () => Promise<any>;
      saveSettings: (settings: any) => Promise<void>;
      selectModelFile: () => Promise<string | null>;
      selectAudioFile: () => Promise<string | null>;
      onSettingsChanged: (callback: (settings: any) => void) => void;
      removeAllListeners: (channel: string) => void;
    };
  }
}