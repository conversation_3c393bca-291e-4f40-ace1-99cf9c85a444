import { BrowserWindow, screen, ipcMain } from 'electron';
import { WindowManager, WindowConfig } from '../WindowManager';

// Mock Electron modules
jest.mock('electron', () => ({
  BrowserWindow: jest.fn().mockImplementation(() => ({
    setIgnoreMouseEvents: jest.fn(),
    setAlwaysOnTop: jest.fn(),
    setPosition: jest.fn(),
    getSize: jest.fn().mockReturnValue([300, 400]),
    getBounds: jest.fn().mockReturnValue({ x: 100, y: 100, width: 300, height: 400 }),
    show: jest.fn(),
    hide: jest.fn(),
    close: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    loadURL: jest.fn(),
    loadFile: jest.fn(),
  })),
  screen: {
    getPrimaryDisplay: jest.fn().mockReturnValue({
      workAreaSize: { width: 1920, height: 1080 }
    })
  },
  ipcMain: {
    handle: jest.fn(),
  },
}));

describe('WindowManager', () => {
  let windowManager: WindowManager;
  let mockWindow: any;

  beforeEach(() => {
    jest.clearAllMocks();
    windowManager = new WindowManager();
    mockWindow = {
      setIgnoreMouseEvents: jest.fn(),
      setAlwaysOnTop: jest.fn(),
      setPosition: jest.fn(),
      getSize: jest.fn().mockReturnValue([300, 400]),
      getBounds: jest.fn().mockReturnValue({ x: 100, y: 100, width: 300, height: 400 }),
      show: jest.fn(),
      hide: jest.fn(),
      close: jest.fn(),
      on: jest.fn(),
      once: jest.fn(),
    };
    (BrowserWindow as unknown as jest.Mock).mockReturnValue(mockWindow);
  });

  describe('createMainWindow', () => {
    it('should create window with default configuration', () => {
      const window = windowManager.createMainWindow();

      expect(BrowserWindow).toHaveBeenCalledWith(
        expect.objectContaining({
          width: 300,
          height: 400,
          transparent: true,
          alwaysOnTop: true,
          frame: false,
          resizable: false,
          skipTaskbar: true,
          show: false,
        })
      );
      expect(window).toBe(mockWindow);
    });

    it('should create window with custom configuration', () => {
      const customConfig: Partial<WindowConfig> = {
        width: 500,
        height: 600,
        x: 200,
        y: 300,
      };

      windowManager.createMainWindow(customConfig);

      expect(BrowserWindow).toHaveBeenCalledWith(
        expect.objectContaining({
          width: 500,
          height: 600,
          x: 200,
          y: 300,
        })
      );
    });

    it('should set default position when not specified', () => {
      windowManager.createMainWindow();

      expect(BrowserWindow).toHaveBeenCalledWith(
        expect.objectContaining({
          x: 1520, // 1920 - 300 - 100
          y: 580,  // 1080 - 400 - 100
        })
      );
    });
  });

  describe('setClickThrough', () => {
    beforeEach(() => {
      windowManager.createMainWindow();
    });

    it('should enable click through', () => {
      windowManager.setClickThrough(true);
      expect(mockWindow.setIgnoreMouseEvents).toHaveBeenCalledWith(true, { forward: true });
    });

    it('should disable click through', () => {
      windowManager.setClickThrough(false);
      expect(mockWindow.setIgnoreMouseEvents).toHaveBeenCalledWith(false);
    });

    it('should handle null window gracefully', () => {
      const emptyWindowManager = new WindowManager();
      expect(() => emptyWindowManager.setClickThrough(true)).not.toThrow();
    });
  });

  describe('setAlwaysOnTop', () => {
    beforeEach(() => {
      windowManager.createMainWindow();
    });

    it('should set always on top', () => {
      windowManager.setAlwaysOnTop(true);
      expect(mockWindow.setAlwaysOnTop).toHaveBeenCalledWith(true);
    });

    it('should disable always on top', () => {
      windowManager.setAlwaysOnTop(false);
      expect(mockWindow.setAlwaysOnTop).toHaveBeenCalledWith(false);
    });
  });

  describe('moveWindow', () => {
    beforeEach(() => {
      windowManager.createMainWindow();
    });

    it('should move window to specified position', () => {
      windowManager.moveWindow(100, 200);
      expect(mockWindow.setPosition).toHaveBeenCalledWith(100, 200);
    });

    it('should clamp position to screen boundaries', () => {
      // Test negative coordinates
      windowManager.moveWindow(-50, -100);
      expect(mockWindow.setPosition).toHaveBeenCalledWith(0, 0);

      // Test coordinates beyond screen
      windowManager.moveWindow(2000, 1200);
      expect(mockWindow.setPosition).toHaveBeenCalledWith(1620, 680); // 1920-300, 1080-400
    });
  });

  describe('drag functionality', () => {
    beforeEach(() => {
      windowManager.createMainWindow();
    });

    it('should start drag with correct offset', () => {
      windowManager.startDrag(50, 75);
      
      // Verify drag state is set (internal state, tested through behavior)
      windowManager.handleDragMove(200, 300);
      expect(mockWindow.setPosition).toHaveBeenCalledWith(150, 225); // 200-50, 300-75
    });

    it('should stop drag', () => {
      windowManager.startDrag(50, 75);
      windowManager.stopDrag();
      
      // After stopping drag, handleDragMove should not move window
      mockWindow.setPosition.mockClear();
      windowManager.handleDragMove(200, 300);
      expect(mockWindow.setPosition).not.toHaveBeenCalled();
    });

    it('should handle drag move correctly', () => {
      windowManager.startDrag(10, 20);
      windowManager.handleDragMove(110, 120);
      
      expect(mockWindow.setPosition).toHaveBeenCalledWith(100, 100);
    });
  });

  describe('window visibility', () => {
    beforeEach(() => {
      windowManager.createMainWindow();
    });

    it('should show window', () => {
      windowManager.showWindow();
      expect(mockWindow.show).toHaveBeenCalled();
    });

    it('should hide window', () => {
      windowManager.hideWindow();
      expect(mockWindow.hide).toHaveBeenCalled();
    });

    it('should close window', () => {
      windowManager.closeWindow();
      expect(mockWindow.close).toHaveBeenCalled();
    });
  });

  describe('getWindowBounds', () => {
    it('should return window bounds', () => {
      windowManager.createMainWindow();
      const bounds = windowManager.getWindowBounds();
      
      expect(bounds).toEqual({ x: 100, y: 100, width: 300, height: 400 });
      expect(mockWindow.getBounds).toHaveBeenCalled();
    });

    it('should return null when no window exists', () => {
      const bounds = windowManager.getWindowBounds();
      expect(bounds).toBeNull();
    });
  });

  describe('IPC handlers', () => {
    it('should setup IPC handlers', () => {
      expect(ipcMain.handle).toHaveBeenCalledWith('window-start-drag', expect.any(Function));
      expect(ipcMain.handle).toHaveBeenCalledWith('window-stop-drag', expect.any(Function));
      expect(ipcMain.handle).toHaveBeenCalledWith('window-drag-move', expect.any(Function));
      expect(ipcMain.handle).toHaveBeenCalledWith('window-set-click-through', expect.any(Function));
      expect(ipcMain.handle).toHaveBeenCalledWith('window-set-always-on-top', expect.any(Function));
      expect(ipcMain.handle).toHaveBeenCalledWith('window-show', expect.any(Function));
      expect(ipcMain.handle).toHaveBeenCalledWith('window-hide', expect.any(Function));
      expect(ipcMain.handle).toHaveBeenCalledWith('window-get-bounds', expect.any(Function));
    });
  });
});