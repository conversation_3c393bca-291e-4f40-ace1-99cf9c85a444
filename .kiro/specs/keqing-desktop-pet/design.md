# 设计文档

## 概述

刻晴3D桌面宠物是一个基于Electron的跨平台桌面应用程序，使用Three.js进行3D渲染，支持MMD格式的3D模型。应用采用模块化架构，分离渲染、交互、语音和系统集成等功能模块，确保代码的可维护性和扩展性。

## 架构

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Electron Main Process                    │
├─────────────────────────────────────────────────────────────┤
│  System Integration  │  Window Management  │  File System   │
│  - Tray Icon        │  - Always On Top    │  - Model Files │
│  - Auto Start       │  - Transparent BG   │  - Audio Files │
│  - Platform APIs    │  - Drag & Drop      │  - Settings    │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   Electron Renderer Process                 │
├─────────────────────────────────────────────────────────────┤
│                        React App                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   3D Engine │  │ Voice Engine│  │ UI Manager  │        │
│  │ (Three.js)  │  │             │  │             │        │
│  │             │  │             │  │             │        │
│  │ - MMD Model │  │ - STT       │  │ - Context   │        │
│  │ - Animation │  │ - TTS       │  │ - Settings  │        │
│  │ - Rendering │  │ - AI Chat   │  │ - Menu      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈选择

- **应用框架**: Electron 28+ - 提供跨平台桌面应用能力
- **前端框架**: React 18 + TypeScript - 组件化开发和类型安全
- **3D渲染引擎**: Three.js r158+ - 强大的WebGL 3D渲染库
- **MMD支持**: @pixiv/three-vrm + mmd-parser - MMD模型加载和动画
- **状态管理**: Zustand - 轻量级状态管理
- **语音识别**: Web Speech API (SpeechRecognition)
- **语音合成**: 自定义TTS引擎 + 刻晴声线模型
- **AI对话**: 豆包大模型API集成

## 组件和接口

### 核心组件

#### 1. 3D渲染引擎 (3DEngine)

```typescript
interface I3DEngine {
  loadModel(modelPath: string): Promise<MMDModel>
  playAnimation(animationType: AnimationType): void
  updateModelPosition(x: number, y: number): void
  setModelScale(scale: number): void
  dispose(): void
}

enum AnimationType {
  IDLE = 'idle',
  TALKING = 'talking',
  HAPPY = 'happy',
  SURPRISED = 'surprised',
  THINKING = 'thinking'
}
```

#### 2. 语音引擎 (VoiceEngine)

```typescript
interface IVoiceEngine {
  startRecording(): Promise<void>
  stopRecording(): Promise<string>
  synthesizeSpeech(text: string, voiceProfile: VoiceProfile): Promise<AudioBuffer>
  playAudio(audioBuffer: AudioBuffer): Promise<void>
  loadVoiceProfile(audioSamples: AudioFile[]): Promise<VoiceProfile>
}

interface VoiceProfile {
  id: string
  name: string
  modelPath: string
  sampleRate: number
  characteristics: VoiceCharacteristics
}
```

#### 3. AI对话管理器 (ChatManager)

```typescript
interface IChatManager {
  processUserInput(input: string): Promise<string>
  setCharacterProfile(profile: CharacterProfile): void
  getContextualResponse(context: ConversationContext): Promise<string>
  initializeDoubaoAPI(endpoint: string): void
}

interface CharacterProfile {
  name: string
  personality: string[]
  speechPatterns: string[]
  knowledgeBase: string[]
  responseTemplates: ResponseTemplate[]
  doubaoSystemPrompt: string // 豆包模型的系统提示词
}

interface DoubaoAPIConfig {
  endpoint: string
  model: string // 使用的豆包模型版本
  maxTokens: number
  temperature: number
  // apiKey 从环境变量 DOUBAO_API_KEY 读取
}

// 环境变量配置
interface EnvironmentConfig {
  DOUBAO_API_KEY: string // 豆包API密钥
}
```

#### 4. 窗口管理器 (WindowManager)

```typescript
interface IWindowManager {
  createOverlayWindow(): BrowserWindow
  setAlwaysOnTop(enabled: boolean): void
  setTransparentBackground(enabled: boolean): void
  enableDragAndDrop(): void
  setClickThrough(enabled: boolean): void
}
```

#### 5. 系统集成管理器 (SystemManager)

```typescript
interface ISystemManager {
  createTrayIcon(): Tray
  setAutoStart(enabled: boolean): void
  registerGlobalShortcuts(): void
  handlePlatformSpecificFeatures(): void
}
```

### 数据接口

#### 模型数据结构

```typescript
interface MMDModel {
  mesh: THREE.Mesh
  skeleton: THREE.Skeleton
  animations: THREE.AnimationClip[]
  materials: THREE.Material[]
  textures: THREE.Texture[]
}

interface ModelAssets {
  modelFile: string // .pmx文件路径
  textureFiles: string[] // 贴图文件路径
  animationFiles: string[] // .vmd动画文件路径
}
```

#### 配置数据结构

```typescript
interface AppConfig {
  display: DisplayConfig
  audio: AudioConfig
  ai: AIConfig
  behavior: BehaviorConfig
  system: SystemConfig
}

interface DisplayConfig {
  modelScale: number
  animationSpeed: number
  position: { x: number; y: number }
  alwaysOnTop: boolean
}

interface AudioConfig {
  volume: number
  voiceProfile: string
  enableVoiceChat: boolean
  microphoneDevice: string
}

interface AIConfig {
  doubaoEndpoint: string
  doubaoModel: string
  systemPrompt: string
  conversationHistory: boolean
  maxHistoryLength: number
  // API密钥从环境变量 DOUBAO_API_KEY 获取
}
```

## 数据模型

### 文件系统结构

```
app/
├── assets/
│   ├── models/
│   │   ├── keqing.pmx          # 3D模型文件
│   │   ├── textures/           # 贴图文件夹
│   │   └── animations/         # 动画文件夹
│   ├── audio/
│   │   ├── voice-samples/      # 声线样本
│   │   └── sound-effects/      # 音效文件
│   └── character/
│       └── keqing-profile.json # 角色资料
├── src/
│   ├── main/                   # Electron主进程
│   ├── renderer/               # 渲染进程
│   └── shared/                 # 共享代码
└── config/
    ├── app-config.json         # 应用配置
    ├── user-settings.json      # 用户设置
    └── doubao-config.json      # 豆包API配置
```

### 数据持久化

- **用户设置**: 存储在用户目录的JSON文件中
- **模型缓存**: 使用IndexedDB缓存已加载的3D模型
- **语音配置**: 存储声线模型和配置参数
- **对话历史**: 可选的对话记录存储

## 错误处理

### 错误分类和处理策略

#### 1. 3D渲染错误
- **WebGL不支持**: 降级到2D模式或显示错误提示
- **模型加载失败**: 使用默认模型或重试机制
- **动画播放错误**: 回退到静态显示

#### 2. 语音处理错误
- **麦克风权限被拒**: 显示权限请求提示
- **语音识别失败**: 提供文字输入备选方案
- **TTS合成失败**: 使用系统默认TTS或文字显示

#### 3. 系统集成错误
- **托盘图标创建失败**: 仅在窗口中显示控制选项
- **自启动设置失败**: 提示用户手动设置
- **文件权限错误**: 引导用户授予必要权限

#### 4. API和网络错误
- **豆包API密钥缺失**: 检查环境变量DOUBAO_API_KEY并提示用户设置
- **网络连接失败**: 提供离线模式或重试机制
- **API调用限制**: 实现请求频率控制和错误提示

### 错误恢复机制

```typescript
interface ErrorHandler {
  handleRenderError(error: RenderError): void
  handleAudioError(error: AudioError): void
  handleSystemError(error: SystemError): void
  recoverFromCriticalError(): Promise<boolean>
}

enum ErrorSeverity {
  LOW = 'low',        // 功能降级
  MEDIUM = 'medium',  // 部分功能不可用
  HIGH = 'high',      // 需要重启应用
  CRITICAL = 'critical' // 应用无法继续运行
}
```

## 测试策略

### 测试层级

#### 1. 单元测试
- **3D引擎组件**: 模型加载、动画播放、渲染管道
- **语音引擎组件**: 录音、识别、合成功能
- **配置管理**: 设置读写、验证逻辑
- **工具函数**: 数据转换、格式化等

#### 2. 集成测试
- **组件间通信**: 3D引擎与语音引擎的协调
- **系统API集成**: 托盘、自启动、文件系统
- **跨平台兼容性**: Windows和Mac平台功能一致性

#### 3. 端到端测试
- **完整用户流程**: 启动→交互→设置→退出
- **语音对话流程**: 录音→识别→回复→播放
- **拖拽和窗口管理**: 移动、置顶、隐藏显示

#### 4. 性能测试
- **资源使用监控**: CPU、内存、GPU使用率
- **渲染性能**: 帧率稳定性、动画流畅度
- **启动时间**: 应用启动和模型加载速度

### 测试工具和框架

- **单元测试**: Jest + @testing-library/react
- **E2E测试**: Playwright + Electron
- **性能测试**: 自定义性能监控工具
- **跨平台测试**: GitHub Actions CI/CD