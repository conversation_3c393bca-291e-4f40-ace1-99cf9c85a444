# 刻晴3D桌面宠物

一个基于Electron的跨平台3D桌面宠物应用，featuring刻晴角色。

## 功能特性

- 🎮 **3D刻晴形象** - 支持MMD模型和动画
- 🗣️ **智能语音对话** - 集成豆包AI和刻晴声线
- 🖱️ **丰富交互** - 拖拽移动、点击对话、右键菜单
- 🔧 **系统集成** - 托盘图标、自启动、跨平台支持
- ⚡ **性能优化** - 低资源占用，不影响日常工作

## 技术栈

- **应用框架**: Electron 28+
- **前端**: React 18 + TypeScript
- **3D渲染**: Three.js + MMD支持
- **状态管理**: Zustand
- **AI对话**: 豆包大模型API
- **构建工具**: Webpack + TypeScript

## 开发环境设置

### 前置要求

- Node.js 18+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 环境变量配置

创建 `.env` 文件并设置豆包API密钥：

```bash
DOUBAO_API_KEY=your_doubao_api_key_here
```

### 开发模式

```bash
npm run dev
```

### 构建应用

```bash
npm run build
```

### 打包分发

```bash
npm run dist
```

## 项目结构

```
src/
├── main/           # Electron主进程
├── renderer/       # React渲染进程
├── shared/         # 共享代码和类型
└── test/          # 测试配置

assets/
├── models/         # 3D模型文件
├── audio/          # 音频文件
└── character/      # 角色资料
```

## 开发指南

### 添加新功能

1. 在 `src/shared/types.ts` 中定义类型
2. 在相应模块中实现功能
3. 编写单元测试
4. 更新文档

### 测试

```bash
# 运行所有测试
npm test

# 监听模式
npm run test:watch

# 生成覆盖率报告
npm test -- --coverage
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License