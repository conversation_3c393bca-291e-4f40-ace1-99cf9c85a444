# VMD动画使用指南

## 🎭 VMD动画功能

刻晴3D桌面宠物现在支持VMD（Vocaloid Motion Data）动画文件！我们已经集成了"杂鱼 杂鱼（绝区零）.vmd"动作数据。

## 🎮 交互方式

### 基础交互
- **左键点击**：播放随机动画（开心、惊讶、挥手）
- **右键点击**：播放VMD舞蹈动画（杂鱼动作）
- **双击**：播放VMD走路动画
- **30秒无交互**：自动播放待机动画

### 动画类型

#### 内置动画
1. **待机动画** - 轻微的呼吸效果
2. **开心动画** - 跳跃和旋转
3. **惊讶动画** - 快速放大缩小
4. **挥手动画** - 左右摇摆
5. **思考动画** - 轻微摇头
6. **说话动画** - 轻微缩放

#### VMD动画（基于杂鱼动作数据）
1. **舞蹈动画** - 复杂的舞蹈动作序列
   - 持续时间：8秒
   - 循环播放
   - 包含跳跃、旋转、缩放等复合动作

2. **走路动画** - 自然的行走动作
   - 持续时间：4秒
   - 循环播放
   - 前后移动配合轻微上下浮动

## 🎨 动画特性

### 优先级系统
- **说话动画**：优先级 6（最高）
- **惊讶动画**：优先级 5
- **开心/舞蹈动画**：优先级 4
- **挥手/走路动画**：优先级 3
- **思考动画**：优先级 2
- **待机动画**：优先级 1（最低）

### 动画过渡
- 所有动画间都有0.5秒的平滑过渡
- 高优先级动画可以打断低优先级动画
- 动画结束后自动回到待机状态

### 状态管理
- 智能的动画队列系统
- 完整的状态历史记录
- 自动的空闲检测和切换

## 🔧 技术实现

### VMD文件支持
- 支持标准VMD格式的动画文件
- 自动解析骨骼动画和表情动画
- 转换为Three.js兼容的动画剪辑

### 性能优化
- 动画数据缓存机制
- 60FPS流畅播放
- 智能的资源管理

## 📝 控制台输出

应用运行时，控制台会显示详细的动画状态信息：

```
开始加载VMD动画文件...
VMD动画加载成功！可以通过右键菜单播放舞蹈动画
刻晴模型加载成功，动画系统已初始化
动画开始: dancing
播放VMD舞蹈动画！
```

## 🎯 使用建议

1. **右键点击**体验最精彩的VMD舞蹈动画
2. **双击**查看自然的走路动作
3. **左键点击**享受随机的表情动画
4. 让刻晴静置30秒，观察自动待机动画

## 🚀 未来扩展

- 支持更多VMD动画文件
- 自定义动画导入功能
- 动画编辑和混合
- 表情动画支持
- 物理效果集成

---

享受与3D刻晴的互动体验吧！🎉