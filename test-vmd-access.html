<!DOCTYPE html>
<html>
<head>
    <title>VMD文件访问测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>VMD文件访问测试</h1>
    <div id="results"></div>

    <script>
        async function testVMDAccess() {
            const results = document.getElementById('results');
            
            // 测试不同的VMD文件路径
            const paths = [
                './assets/models/你的笑容动作+表情完整整合 - 改.vmd',
                'assets/models/你的笑容动作+表情完整整合 - 改.vmd',
                '/assets/models/你的笑容动作+表情完整整合 - 改.vmd'
            ];
            
            for (const path of paths) {
                try {
                    console.log('🔍 测试路径:', path);
                    const response = await fetch(path, { method: 'HEAD' });
                    
                    const div = document.createElement('div');
                    div.className = response.ok ? 'status success' : 'status error';
                    div.innerHTML = `
                        <strong>路径:</strong> ${path}<br>
                        <strong>状态:</strong> ${response.status} ${response.statusText}<br>
                        <strong>结果:</strong> ${response.ok ? '✅ 文件可访问' : '❌ 文件不可访问'}
                    `;
                    results.appendChild(div);
                    
                    if (response.ok) {
                        // 尝试获取文件大小
                        const fullResponse = await fetch(path);
                        const arrayBuffer = await fullResponse.arrayBuffer();
                        
                        const sizeDiv = document.createElement('div');
                        sizeDiv.className = 'status info';
                        sizeDiv.innerHTML = `
                            <strong>文件大小:</strong> ${(arrayBuffer.byteLength / 1024 / 1024).toFixed(2)} MB<br>
                            <strong>字节数:</strong> ${arrayBuffer.byteLength} bytes
                        `;
                        results.appendChild(sizeDiv);
                        
                        console.log('✅ VMD文件访问成功:', path, '大小:', arrayBuffer.byteLength, 'bytes');
                        break; // 找到可访问的路径就停止
                    }
                } catch (error) {
                    console.error('❌ VMD文件访问失败:', path, error);
                    
                    const div = document.createElement('div');
                    div.className = 'status error';
                    div.innerHTML = `
                        <strong>路径:</strong> ${path}<br>
                        <strong>错误:</strong> ${error.message}<br>
                        <strong>结果:</strong> ❌ 访问异常
                    `;
                    results.appendChild(div);
                }
            }
        }
        
        // 页面加载后开始测试
        window.addEventListener('load', testVMDAccess);
    </script>
</body>
</html>